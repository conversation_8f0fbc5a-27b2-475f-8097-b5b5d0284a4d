import torch
from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer
import argparse

# 创建简单的参数
class Args:
    def __init__(self):
        self.model_path = 'results/my_experiment_phase3/models/celeba_phase3_epoch5_snr5.pth'
        self.trainset = 'celeba'
        self.testset = 'kodak'
        self.data_path = './data'
        self.model = 'WITT_W/O'
        self.C = 512
        self.quant_code_dim = 128
        self.channel_type = 'rayleigh'
        self.multiple_snr = '5'
        self.distortion_metric = 'MSE'
        self.experiment_name = 'default_exp'
        self.num_workers = 0

args = Args()
cfg = BaseConfig(args)

# 创建模型实例
net1 = CoreModel(args, cfg).to(cfg.device)
quantizer = DENSEQuantizer(
    feature_dim=cfg.encoder_kwargs['embed_dims'][-1], code_dim=cfg.quant_code_dim
).to(cfg.device)
interleaver = BlockInterleaver().to(cfg.device)

# 模拟convert_to_onnx.py中的接收端模型
class ReceiverModel(torch.nn.Module):
    def __init__(self, net1, net2, quantizer, interleaver):
        super().__init__()
        self.net1 = net1
        self.net2 = net2
        self.quantizer = quantizer
        self.interleaver = interleaver

    def forward(self, interleaved_feature):
        # 这是原始的有问题的代码
        B, L = interleaved_feature.shape
        metadata = {"x_interleaved_shape": torch.Size([64, B, L // 64])}  # 硬编码64
        
        # 解交织
        deinterleaved_feature = self.interleaver.deinterleave(
            interleaved_feature, metadata
        )
        deinterleaved_feature = deinterleaved_feature.reshape(B * 64, cfg.quant_code_dim)  # 硬编码64
        # 反量化
        dequantized_feature = self.quantizer.dequantize_bits_to_features(
            deinterleaved_feature
        ).reshape(B, 64, -1)  # 硬编码64
        # 解码
        recon_image1 = self.net1.decode(dequantized_feature)
        recon_image2 = self.net2.decode(dequantized_feature)
        return recon_image1, recon_image2

# 正确的接收端模型
class CorrectReceiverModel(torch.nn.Module):
    def __init__(self, net1, net2, quantizer, interleaver):
        super().__init__()
        self.net1 = net1
        self.net2 = net2
        self.quantizer = quantizer
        self.interleaver = interleaver

    def forward(self, interleaved_feature):
        B, L = interleaved_feature.shape
        # 动态计算L_feat
        L_feat = L // cfg.quant_code_dim
        metadata = {"x_interleaved_shape": torch.Size([L_feat, B, cfg.quant_code_dim])}
        
        # 解交织
        deinterleaved_feature = self.interleaver.deinterleave(
            interleaved_feature, metadata
        )
        deinterleaved_feature = deinterleaved_feature.reshape(B * L_feat, cfg.quant_code_dim)
        # 反量化
        dequantized_feature = self.quantizer.dequantize_bits_to_features(
            deinterleaved_feature
        ).reshape(B, L_feat, -1)
        # 解码
        recon_image1 = self.net1.decode(dequantized_feature)
        recon_image2 = self.net2.decode(dequantized_feature)
        return recon_image1, recon_image2

# 测试不同batch size
for batch_size in [1, 2, 4]:
    print(f"\n=== 测试 batch_size = {batch_size} ===")
    
    # 创建测试输入
    dummy_input1 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
    dummy_input2 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
    
    with torch.no_grad():
        # 编码和量化
        feature1 = net1.encode(dummy_input1)
        feature2 = net1.encode(dummy_input2)
        feature = feature1 + feature2
        
        B, L_feat, D_feat = feature.shape
        print(f"特征形状: {feature.shape}")
        
        quantized_feature = quantizer.quantize_features_to_bits(feature.reshape(B * L_feat, D_feat))
        quantized_feature = quantized_feature.reshape(B, L_feat, cfg.quant_code_dim)
        
        # 交织
        interleaved_feature, metadata = interleaver.interleave(quantized_feature)
        print(f"交织后形状: {interleaved_feature.shape}")
        print(f"正确metadata: {metadata}")
        
        # 测试原始的有问题的接收端模型
        try:
            buggy_receiver = ReceiverModel(net1, net1, quantizer, interleaver)
            buggy_result1, buggy_result2 = buggy_receiver(interleaved_feature)
            print(f"✓ 有问题的模型成功: {buggy_result1.shape}")
        except Exception as e:
            print(f"✗ 有问题的模型失败: {e}")
        
        # 测试正确的接收端模型
        try:
            correct_receiver = CorrectReceiverModel(net1, net1, quantizer, interleaver)
            correct_result1, correct_result2 = correct_receiver(interleaved_feature)
            print(f"✓ 正确的模型成功: {correct_result1.shape}")
        except Exception as e:
            print(f"✗ 正确的模型失败: {e}")

print("\n=== 问题总结 ===")
print("问题在于convert_to_onnx.py中硬编码了64这个值")
print("当batch_size > 1时，reshape操作会失败")
print("需要动态计算L_feat而不是硬编码64")
