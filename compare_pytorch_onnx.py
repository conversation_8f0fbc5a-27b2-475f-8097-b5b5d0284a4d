import torch
import numpy as np
import onnxruntime as ort
from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer
from utils import load_individual_model_state
import argparse
from PIL import Image
import torchvision.transforms as transforms

# 创建简单的参数
class Args:
    def __init__(self):
        self.model_path = 'results/my_experiment_phase3/models/celeba_phase3_epoch5_snr5.pth'
        self.trainset = 'celeba'
        self.testset = 'kodak'
        self.data_path = './data'
        self.model = 'WITT_W/O'
        self.C = 512
        self.quant_code_dim = 128
        self.channel_type = 'rayleigh'
        self.multiple_snr = '5'
        self.distortion_metric = 'MSE'
        self.experiment_name = 'default_exp'
        self.num_workers = 0

def load_test_images(image_paths, target_size=(64, 64)):
    """加载测试图像"""
    transform = transforms.Compose([
        transforms.Resize(target_size),
        transforms.ToTensor(),
    ])
    
    images = []
    for path in image_paths:
        try:
            image = Image.open(path).convert('RGB')
            tensor = transform(image)
            images.append(tensor)
        except Exception as e:
            print(f"跳过图像 {path}: {e}")
    
    if len(images) == 0:
        return None
    
    return torch.stack(images, dim=0)

def calculate_psnr(img1, img2):
    """计算PSNR"""
    mse = torch.mean((img1 - img2) ** 2)
    if mse == 0:
        return float('inf')
    return 20 * torch.log10(1.0 / torch.sqrt(mse))

def main():
    args = Args()
    cfg = BaseConfig(args)
    
    print("=== PyTorch vs ONNX 模型对比测试 ===")
    
    # 1. 加载 PyTorch 模型
    print("1. 加载 PyTorch 模型...")
    net1 = CoreModel(args, cfg).to(cfg.device)
    net2 = CoreModel(args, cfg).to(cfg.device)
    quantizer = DENSEQuantizer(
        feature_dim=cfg.encoder_kwargs['embed_dims'][-1], code_dim=cfg.quant_code_dim
    ).to(cfg.device)
    interleaver = BlockInterleaver().to(cfg.device)
    
    try:
        checkpoint = torch.load(args.model_path)
        load_individual_model_state(net1, checkpoint["net1_state_dict"], cfg.device)
        load_individual_model_state(net2, checkpoint["net2_state_dict"], cfg.device)
        load_individual_model_state(quantizer, checkpoint["quantizer_state_dict"], cfg.device)
        print("✓ PyTorch 模型权重加载成功")
    except Exception as e:
        print(f"✗ PyTorch 模型权重加载失败: {e}")
        return
    
    # 设置为评估模式
    net1.eval()
    net2.eval()
    quantizer.eval()
    interleaver.eval()
    
    # 2. 加载 ONNX 模型
    print("2. 加载 ONNX 模型...")
    try:
        transmitter_session = ort.InferenceSession("transmitter_model.onnx")
        receiver_session = ort.InferenceSession("receiver_model.onnx")
        print("✓ ONNX 模型加载成功")
    except Exception as e:
        print(f"✗ ONNX 模型加载失败: {e}")
        return
    
    # 3. 创建测试数据
    print("3. 创建测试数据...")
    batch_size = 2
    dummy_input1 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
    dummy_input2 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
    
    print(f"测试输入形状: {dummy_input1.shape}, {dummy_input2.shape}")
    print(f"输入范围: [{dummy_input1.min():.3f}, {dummy_input1.max():.3f}]")
    
    # 4. PyTorch 模型推理
    print("4. PyTorch 模型推理...")
    with torch.no_grad():
        # 编码
        feature1 = net1.encode(dummy_input1)
        feature2 = net2.encode(dummy_input2)
        feature = feature1 + feature2
        
        B, L_feat, D_feat = feature.shape
        print(f"编码特征形状: {feature.shape}")
        
        # 量化
        quantized_feature = quantizer.quantize_features_to_bits(feature.reshape(B * L_feat, D_feat))
        quantized_feature = quantized_feature.reshape(B, L_feat, cfg.quant_code_dim)
        print(f"量化特征形状: {quantized_feature.shape}")
        
        # 交织
        interleaved_feature, metadata = interleaver.interleave(quantized_feature)
        print(f"交织特征形状: {interleaved_feature.shape}")
        print(f"交织metadata: {metadata}")
        
        # 解交织
        deinterleaved_feature = interleaver.deinterleave(interleaved_feature, metadata)
        print(f"解交织特征形状: {deinterleaved_feature.shape}")
        
        # 反量化
        deinterleaved_flat = deinterleaved_feature.reshape(B * L_feat, cfg.quant_code_dim)
        dequantized_feature = quantizer.dequantize_bits_to_features(deinterleaved_flat)
        dequantized_feature = dequantized_feature.reshape(B, L_feat, -1)
        print(f"反量化特征形状: {dequantized_feature.shape}")
        
        # 解码
        pytorch_recon1 = net1.decode(dequantized_feature)
        pytorch_recon2 = net2.decode(dequantized_feature)
        
        print(f"PyTorch 重建形状: {pytorch_recon1.shape}, {pytorch_recon2.shape}")
        print(f"PyTorch 重建范围: [{pytorch_recon1.min():.3f}, {pytorch_recon1.max():.3f}]")
    
    # 5. ONNX 模型推理
    print("5. ONNX 模型推理...")
    
    # 转换为 numpy
    input1_np = dummy_input1.cpu().numpy().astype(np.float32)
    input2_np = dummy_input2.cpu().numpy().astype(np.float32)
    
    # 发送端推理
    transmitter_outputs = transmitter_session.run(
        None, {"input1": input1_np, "input2": input2_np}
    )
    onnx_interleaved = transmitter_outputs[0]
    print(f"ONNX 发送端输出形状: {onnx_interleaved.shape}")
    
    # 接收端推理
    receiver_outputs = receiver_session.run(
        None, {"interleaved_feature": onnx_interleaved}
    )
    onnx_recon1, onnx_recon2 = receiver_outputs
    print(f"ONNX 重建形状: {onnx_recon1.shape}, {onnx_recon2.shape}")
    print(f"ONNX 重建范围: [{onnx_recon1.min():.3f}, {onnx_recon1.max():.3f}]")
    
    # 6. 对比结果
    print("6. 对比结果...")
    
    # 转换为相同格式
    pytorch_interleaved_np = interleaved_feature.cpu().numpy()
    pytorch_recon1_np = pytorch_recon1.cpu().numpy()
    pytorch_recon2_np = pytorch_recon2.cpu().numpy()
    
    # 对比交织特征
    interleaved_diff = np.max(np.abs(pytorch_interleaved_np - onnx_interleaved))
    print(f"交织特征最大差异: {interleaved_diff:.6f}")
    
    # 对比重建结果
    recon1_diff = np.max(np.abs(pytorch_recon1_np - onnx_recon1))
    recon2_diff = np.max(np.abs(pytorch_recon2_np - onnx_recon2))
    print(f"重建图像1最大差异: {recon1_diff:.6f}")
    print(f"重建图像2最大差异: {recon2_diff:.6f}")
    
    # 计算 PSNR
    pytorch_psnr1 = calculate_psnr(dummy_input1, torch.from_numpy(pytorch_recon1_np).to(cfg.device))
    pytorch_psnr2 = calculate_psnr(dummy_input2, torch.from_numpy(pytorch_recon2_np).to(cfg.device))
    
    onnx_psnr1 = calculate_psnr(dummy_input1, torch.from_numpy(onnx_recon1).to(cfg.device))
    onnx_psnr2 = calculate_psnr(dummy_input2, torch.from_numpy(onnx_recon2).to(cfg.device))
    
    print(f"\nPSNR 对比:")
    print(f"PyTorch - 图像1: {pytorch_psnr1:.2f} dB, 图像2: {pytorch_psnr2:.2f} dB")
    print(f"ONNX    - 图像1: {onnx_psnr1:.2f} dB, 图像2: {onnx_psnr2:.2f} dB")
    print(f"平均PSNR - PyTorch: {(pytorch_psnr1 + pytorch_psnr2) / 2:.2f} dB")
    print(f"平均PSNR - ONNX: {(onnx_psnr1 + onnx_psnr2) / 2:.2f} dB")
    
    # 7. 分析问题
    print("\n7. 问题分析:")
    if interleaved_diff < 1e-5:
        print("✓ 发送端模型输出一致")
    else:
        print(f"✗ 发送端模型输出不一致，差异: {interleaved_diff}")
    
    if recon1_diff < 1e-5 and recon2_diff < 1e-5:
        print("✓ 接收端模型输出一致")
    else:
        print(f"✗ 接收端模型输出不一致")
    
    if pytorch_psnr1 < 15 or pytorch_psnr2 < 15:
        print("⚠️  PyTorch 模型本身的 PSNR 就很低，可能是模型训练问题")
    
    if abs(pytorch_psnr1 - onnx_psnr1) > 1 or abs(pytorch_psnr2 - onnx_psnr2) > 1:
        print("⚠️  PyTorch 和 ONNX 模型的 PSNR 差异较大")
    
    print("\n8. 建议:")
    if pytorch_psnr1 < 15:
        print("- 检查训练好的模型质量")
        print("- 确认模型是否充分训练")
        print("- 检查训练数据和损失函数")
    
    if interleaved_diff > 1e-5:
        print("- 检查 ONNX 转换过程")
        print("- 验证模型权重是否正确加载")

if __name__ == "__main__":
    main()
