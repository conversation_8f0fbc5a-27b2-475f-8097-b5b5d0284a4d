import torch
import numpy as np
import onnxruntime as ort
from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer
from utils import load_individual_model_state
import argparse
from PIL import Image
import torchvision.transforms as transforms
import glob
import os

# 创建简单的参数
class Args:
    def __init__(self):
        self.model_path = 'results/my_experiment_phase3/models/celeba_phase3_epoch5_snr5.pth'
        self.trainset = 'celeba'
        self.testset = 'kodak'
        self.data_path = './data'
        self.model = 'WITT_W/O'
        self.C = 512
        self.quant_code_dim = 128
        self.channel_type = 'rayleigh'
        self.multiple_snr = '5'
        self.distortion_metric = 'MSE'
        self.experiment_name = 'default_exp'
        self.num_workers = 0

def load_real_images(image_dir, num_images=4, target_size=(64, 64)):
    """加载真实图像"""
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp']
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(glob.glob(os.path.join(image_dir, ext)))
        image_files.extend(glob.glob(os.path.join(image_dir, ext.upper())))
    
    if len(image_files) == 0:
        print(f"在目录 {image_dir} 中没有找到图像文件")
        return None
    
    # 限制图像数量
    image_files = image_files[:num_images]
    
    transform = transforms.Compose([
        transforms.Resize(target_size),
        transforms.ToTensor(),  # 转换为 [0, 1] 范围
    ])
    
    images = []
    for path in image_files:
        try:
            image = Image.open(path).convert('RGB')
            tensor = transform(image)
            images.append(tensor)
            print(f"加载图像: {os.path.basename(path)}, 形状: {tensor.shape}, 范围: [{tensor.min():.3f}, {tensor.max():.3f}]")
        except Exception as e:
            print(f"跳过图像 {path}: {e}")
    
    if len(images) == 0:
        return None
    
    return torch.stack(images, dim=0)

def calculate_psnr(img1, img2):
    """计算PSNR"""
    mse = torch.mean((img1 - img2) ** 2)
    if mse == 0:
        return float('inf')
    return 20 * torch.log10(1.0 / torch.sqrt(mse))

def save_comparison_images(original, reconstructed, prefix="comparison"):
    """保存对比图像"""
    from torchvision.utils import save_image
    
    # 确保在 [0, 1] 范围内
    original = torch.clamp(original, 0, 1)
    reconstructed = torch.clamp(reconstructed, 0, 1)
    
    # 保存原始图像
    save_image(original, f"{prefix}_original.png", nrow=2, padding=2)
    
    # 保存重建图像
    save_image(reconstructed, f"{prefix}_reconstructed.png", nrow=2, padding=2)
    
    # 保存对比图像
    comparison = torch.cat([original, reconstructed], dim=0)
    save_image(comparison, f"{prefix}_comparison.png", nrow=2, padding=2)
    
    print(f"保存对比图像: {prefix}_*.png")

def main():
    args = Args()
    cfg = BaseConfig(args)
    
    print("=== 使用真实图像测试 PyTorch vs ONNX 模型 ===")
    
    # 1. 加载 PyTorch 模型
    print("1. 加载 PyTorch 模型...")
    net1 = CoreModel(args, cfg).to(cfg.device)
    net2 = CoreModel(args, cfg).to(cfg.device)
    quantizer = DENSEQuantizer(
        feature_dim=cfg.encoder_kwargs['embed_dims'][-1], code_dim=cfg.quant_code_dim
    ).to(cfg.device)
    interleaver = BlockInterleaver().to(cfg.device)
    
    try:
        checkpoint = torch.load(args.model_path)
        load_individual_model_state(net1, checkpoint["net1_state_dict"], cfg.device)
        load_individual_model_state(net2, checkpoint["net2_state_dict"], cfg.device)
        load_individual_model_state(quantizer, checkpoint["quantizer_state_dict"], cfg.device)
        print("✓ PyTorch 模型权重加载成功")
    except Exception as e:
        print(f"✗ PyTorch 模型权重加载失败: {e}")
        return
    
    # 设置为评估模式
    net1.eval()
    net2.eval()
    quantizer.eval()
    interleaver.eval()
    
    # 2. 加载 ONNX 模型
    print("2. 加载 ONNX 模型...")
    try:
        transmitter_session = ort.InferenceSession("transmitter_model.onnx")
        receiver_session = ort.InferenceSession("receiver_model.onnx")
        print("✓ ONNX 模型加载成功")
    except Exception as e:
        print(f"✗ ONNX 模型加载失败: {e}")
        return
    
    # 3. 加载真实图像
    print("3. 加载真实图像...")
    image_dir = "D:/data/CelebA64/test/images"
    if not os.path.exists(image_dir):
        print(f"图像目录不存在: {image_dir}")
        print("使用随机数据进行测试...")
        # 使用正确范围的随机数据
        test_images = torch.rand(4, 3, 64, 64).to(cfg.device)  # [0, 1] 范围
    else:
        test_images = load_real_images(image_dir, num_images=4)
        if test_images is None:
            print("无法加载真实图像，使用随机数据...")
            test_images = torch.rand(4, 3, 64, 64).to(cfg.device)
        else:
            test_images = test_images.to(cfg.device)
    
    # 分成两组
    batch_size = test_images.shape[0] // 2
    input1 = test_images[:batch_size]
    input2 = test_images[batch_size:batch_size*2]
    
    print(f"测试输入形状: {input1.shape}, {input2.shape}")
    print(f"输入范围: [{input1.min():.3f}, {input1.max():.3f}]")
    
    # 4. PyTorch 模型推理
    print("4. PyTorch 模型推理...")
    with torch.no_grad():
        # 编码
        feature1 = net1.encode(input1)
        feature2 = net2.encode(input2)
        feature = feature1 + feature2
        
        B, L_feat, D_feat = feature.shape
        print(f"编码特征形状: {feature.shape}")
        
        # 量化
        quantized_feature = quantizer.quantize_features_to_bits(feature.reshape(B * L_feat, D_feat))
        quantized_feature = quantized_feature.reshape(B, L_feat, cfg.quant_code_dim)
        
        # 交织
        interleaved_feature, metadata = interleaver.interleave(quantized_feature)
        
        # 解交织
        deinterleaved_feature = interleaver.deinterleave(interleaved_feature, metadata)
        
        # 反量化
        deinterleaved_flat = deinterleaved_feature.reshape(B * L_feat, cfg.quant_code_dim)
        dequantized_feature = quantizer.dequantize_bits_to_features(deinterleaved_flat)
        dequantized_feature = dequantized_feature.reshape(B, L_feat, -1)
        
        # 解码
        pytorch_recon1 = net1.decode(dequantized_feature)
        pytorch_recon2 = net2.decode(dequantized_feature)
        
        print(f"PyTorch 重建形状: {pytorch_recon1.shape}, {pytorch_recon2.shape}")
        print(f"PyTorch 重建范围: [{pytorch_recon1.min():.3f}, {pytorch_recon1.max():.3f}]")
    
    # 5. ONNX 模型推理
    print("5. ONNX 模型推理...")
    
    # 转换为 numpy
    input1_np = input1.cpu().numpy().astype(np.float32)
    input2_np = input2.cpu().numpy().astype(np.float32)
    
    # 发送端推理
    transmitter_outputs = transmitter_session.run(
        None, {"input1": input1_np, "input2": input2_np}
    )
    onnx_interleaved = transmitter_outputs[0]
    
    # 接收端推理
    receiver_outputs = receiver_session.run(
        None, {"interleaved_feature": onnx_interleaved}
    )
    onnx_recon1, onnx_recon2 = receiver_outputs
    print(f"ONNX 重建形状: {onnx_recon1.shape}, {onnx_recon2.shape}")
    print(f"ONNX 重建范围: [{onnx_recon1.min():.3f}, {onnx_recon1.max():.3f}]")
    
    # 6. 对比结果
    print("6. 对比结果...")
    
    # 转换为相同格式
    pytorch_interleaved_np = interleaved_feature.cpu().numpy()
    pytorch_recon1_np = pytorch_recon1.cpu().numpy()
    pytorch_recon2_np = pytorch_recon2.cpu().numpy()
    
    # 对比交织特征
    interleaved_diff = np.max(np.abs(pytorch_interleaved_np - onnx_interleaved))
    print(f"交织特征最大差异: {interleaved_diff:.6f}")
    
    # 对比重建结果
    recon1_diff = np.max(np.abs(pytorch_recon1_np - onnx_recon1))
    recon2_diff = np.max(np.abs(pytorch_recon2_np - onnx_recon2))
    print(f"重建图像1最大差异: {recon1_diff:.6f}")
    print(f"重建图像2最大差异: {recon2_diff:.6f}")
    
    # 计算 PSNR
    pytorch_psnr1 = calculate_psnr(input1, pytorch_recon1)
    pytorch_psnr2 = calculate_psnr(input2, pytorch_recon2)
    
    onnx_psnr1 = calculate_psnr(input1, torch.from_numpy(onnx_recon1).to(cfg.device))
    onnx_psnr2 = calculate_psnr(input2, torch.from_numpy(onnx_recon2).to(cfg.device))
    
    print(f"\nPSNR 对比:")
    print(f"PyTorch - 图像1: {pytorch_psnr1:.2f} dB, 图像2: {pytorch_psnr2:.2f} dB")
    print(f"ONNX    - 图像1: {onnx_psnr1:.2f} dB, 图像2: {onnx_psnr2:.2f} dB")
    print(f"平均PSNR - PyTorch: {(pytorch_psnr1 + pytorch_psnr2) / 2:.2f} dB")
    print(f"平均PSNR - ONNX: {(onnx_psnr1 + onnx_psnr2) / 2:.2f} dB")
    
    # 7. 保存对比图像
    print("7. 保存对比图像...")
    save_comparison_images(input1, pytorch_recon1, "pytorch_recon1")
    save_comparison_images(input2, pytorch_recon2, "pytorch_recon2")
    save_comparison_images(input1, torch.from_numpy(onnx_recon1).to(cfg.device), "onnx_recon1")
    save_comparison_images(input2, torch.from_numpy(onnx_recon2).to(cfg.device), "onnx_recon2")
    
    # 8. 分析结果
    print("\n8. 结果分析:")
    if pytorch_psnr1 > 20 and pytorch_psnr2 > 20:
        print("✓ PyTorch 模型质量良好")
    else:
        print(f"⚠️  PyTorch 模型质量较低 (PSNR < 20 dB)")
    
    if abs(pytorch_psnr1 - onnx_psnr1) < 1 and abs(pytorch_psnr2 - onnx_psnr2) < 1:
        print("✓ ONNX 模型与 PyTorch 模型结果一致")
    else:
        print("⚠️  ONNX 模型与 PyTorch 模型结果存在差异")
    
    if interleaved_diff < 1e-5:
        print("✓ 发送端模型转换正确")
    else:
        print(f"⚠️  发送端模型转换存在问题，差异: {interleaved_diff}")

if __name__ == "__main__":
    main()
