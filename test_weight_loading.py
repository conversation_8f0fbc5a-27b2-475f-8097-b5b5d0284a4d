import torch
from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer
from utils import load_individual_model_state
import argparse

# 创建简单的参数
class Args:
    def __init__(self):
        self.model_path = 'results/my_experiment_phase3/models/celeba_phase3_epoch5_snr5.pth'
        self.trainset = 'celeba'
        self.testset = 'kodak'
        self.data_path = './data'
        self.model = 'WITT_W/O'
        self.C = 512
        self.quant_code_dim = 128
        self.channel_type = 'rayleigh'
        self.multiple_snr = '5'
        self.distortion_metric = 'MSE'
        self.experiment_name = 'default_exp'
        self.num_workers = 0

args = Args()
cfg = BaseConfig(args)

print("=== 测试权重加载 ===")

# 创建模型实例（未加载权重）
net1_before = CoreModel(args, cfg).to(cfg.device)
net2_before = CoreModel(args, cfg).to(cfg.device)
quantizer_before = DENSEQuantizer(
    feature_dim=cfg.encoder_kwargs['embed_dims'][-1], code_dim=cfg.quant_code_dim
).to(cfg.device)

print("加载权重前:")
print(f"net1第一个参数: {list(net1_before.parameters())[0].flatten()[:3]}")
print(f"net2第一个参数: {list(net2_before.parameters())[0].flatten()[:3]}")

# 检查两个网络是否相同（应该相同，因为都是随机初始化）
net1_params = list(net1_before.parameters())
net2_params = list(net2_before.parameters())

all_same_before = True
for i, (p1, p2) in enumerate(zip(net1_params, net2_params)):
    if not torch.equal(p1, p2):
        all_same_before = False
        break

print(f"加载权重前net1和net2是否相同: {all_same_before}")

# 加载权重
try:
    checkpoint = torch.load(args.model_path)
    print(f"Checkpoint keys: {checkpoint.keys()}")
    
    # 创建新的模型实例来加载权重
    net1_after = CoreModel(args, cfg).to(cfg.device)
    net2_after = CoreModel(args, cfg).to(cfg.device)
    quantizer_after = DENSEQuantizer(
        feature_dim=cfg.encoder_kwargs['embed_dims'][-1], code_dim=cfg.quant_code_dim
    ).to(cfg.device)
    
    # 加载权重
    load_individual_model_state(net1_after, checkpoint["net1_state_dict"], cfg.device)
    load_individual_model_state(net2_after, checkpoint["net2_state_dict"], cfg.device)
    load_individual_model_state(quantizer_after, checkpoint["quantizer_state_dict"], cfg.device)
    
    print("\n加载权重后:")
    print(f"net1第一个参数: {list(net1_after.parameters())[0].flatten()[:3]}")
    print(f"net2第一个参数: {list(net2_after.parameters())[0].flatten()[:3]}")
    
    # 检查加载后的权重是否不同
    net1_params_after = list(net1_after.parameters())
    net2_params_after = list(net2_after.parameters())
    
    all_same_after = True
    max_diff = 0
    for i, (p1, p2) in enumerate(zip(net1_params_after, net2_params_after)):
        if not torch.equal(p1, p2):
            diff = torch.max(torch.abs(p1 - p2))
            max_diff = max(max_diff, diff.item())
            all_same_after = False
    
    print(f"加载权重后net1和net2是否相同: {all_same_after}")
    if not all_same_after:
        print(f"最大权重差异: {max_diff}")
    
    # 设置为评估模式
    net1_after.eval()
    net2_after.eval()
    quantizer_after.eval()
    
    # 测试推理一致性
    print("\n=== 测试推理一致性 ===")
    batch_size = 1
    dummy_input = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
    
    with torch.no_grad():
        # 编码
        feature1 = net1_after.encode(dummy_input)
        feature2 = net2_after.encode(dummy_input)
        print(f"编码特征差异: {torch.max(torch.abs(feature1 - feature2))}")
        
        # 解码同一个特征
        test_feature = torch.randn(batch_size, 64, 512).to(cfg.device)
        recon1 = net1_after.decode(test_feature)
        recon2 = net2_after.decode(test_feature)
        print(f"解码结果差异: {torch.max(torch.abs(recon1 - recon2))}")
        
        # 多次解码测试一致性
        recon1_again = net1_after.decode(test_feature)
        recon2_again = net2_after.decode(test_feature)
        
        print(f"net1多次解码一致性: {torch.equal(recon1, recon1_again)}")
        print(f"net2多次解码一致性: {torch.equal(recon2, recon2_again)}")
        
        if not torch.equal(recon1, recon1_again):
            print(f"net1多次解码差异: {torch.max(torch.abs(recon1 - recon1_again))}")
        if not torch.equal(recon2, recon2_again):
            print(f"net2多次解码差异: {torch.max(torch.abs(recon2 - recon2_again))}")

except Exception as e:
    print(f"权重加载失败: {e}")

print("\n=== 结论 ===")
print("如果net1和net2的权重不同，这是正常的，因为它们在训练中分别学习")
print("如果同一网络多次推理结果不同，可能存在以下问题:")
print("1. 网络中有Dropout等随机层（应该在eval模式下关闭）")
print("2. BatchNorm等层的状态问题")
print("3. 网络内部有其他状态相关的操作")
