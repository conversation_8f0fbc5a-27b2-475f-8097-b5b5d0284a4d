import torch
from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer
import argparse

# 创建简单的参数
class Args:
    def __init__(self):
        self.model_path = 'results/my_experiment_phase3/models/celeba_phase3_epoch5_snr5.pth'
        self.trainset = 'celeba'
        self.testset = 'kodak'
        self.data_path = './data'
        self.model = 'WITT_W/O'
        self.C = 512
        self.quant_code_dim = 128
        self.channel_type = 'rayleigh'
        self.multiple_snr = '5'
        self.distortion_metric = 'MSE'
        self.experiment_name = 'default_exp'
        self.num_workers = 0

args = Args()
cfg = BaseConfig(args)

# 创建模型实例
net1 = CoreModel(args, cfg).to(cfg.device)
quantizer = DENSEQuantizer(
    feature_dim=cfg.encoder_kwargs['embed_dims'][-1], code_dim=cfg.quant_code_dim
).to(cfg.device)
interleaver = BlockInterleaver().to(cfg.device)

# 测试交织器的行为
batch_size = 2
dummy_input1 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
dummy_input2 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)

print("=== 调试交织器行为 ===")

with torch.no_grad():
    # 编码
    feature1 = net1.encode(dummy_input1)
    feature2 = net1.encode(dummy_input2)
    feature = feature1 + feature2
    
    B, L_feat, D_feat = feature.shape
    print(f"原始特征形状: {feature.shape}")
    
    # 量化
    quantized_feature = quantizer.quantize_features_to_bits(feature.reshape(B * L_feat, D_feat))
    quantized_feature = quantized_feature.reshape(B, L_feat, cfg.quant_code_dim)
    print(f"量化后特征形状: {quantized_feature.shape}")
    
    # 交织
    interleaved_feature, correct_metadata = interleaver.interleave(quantized_feature)
    print(f"交织后形状: {interleaved_feature.shape}")
    print(f"正确metadata: {correct_metadata}")
    
    # 正确的解交织
    correct_deinterleaved = interleaver.deinterleave(interleaved_feature, correct_metadata)
    print(f"正确解交织后形状: {correct_deinterleaved.shape}")
    
    # 检查是否完全恢复
    if torch.equal(quantized_feature, correct_deinterleaved):
        print("✓ 正确的交织/解交织完全恢复")
    else:
        diff = torch.max(torch.abs(quantized_feature - correct_deinterleaved))
        print(f"✗ 正确的交织/解交织有差异: {diff}")
    
    # 模拟ONNX中的错误metadata
    B_inter, L_inter = interleaved_feature.shape
    wrong_metadata = {"x_interleaved_shape": torch.Size([64, B_inter, L_inter // 64])}
    print(f"错误metadata: {wrong_metadata}")
    
    # 用错误metadata解交织
    try:
        wrong_deinterleaved = interleaver.deinterleave(interleaved_feature, wrong_metadata)
        print(f"错误解交织后形状: {wrong_deinterleaved.shape}")
        
        # 检查差异
        if torch.equal(quantized_feature, wrong_deinterleaved):
            print("✓ 错误metadata竟然也产生了正确结果")
        else:
            diff = torch.max(torch.abs(quantized_feature - wrong_deinterleaved))
            print(f"✗ 错误metadata产生差异: {diff}")
            
            # 详细比较
            print(f"原始特征前5个元素: {quantized_feature.flatten()[:5]}")
            print(f"正确解交织前5个元素: {correct_deinterleaved.flatten()[:5]}")
            print(f"错误解交织前5个元素: {wrong_deinterleaved.flatten()[:5]}")
            
    except Exception as e:
        print(f"错误metadata解交织失败: {e}")

print("\n=== 分析交织器内部逻辑 ===")
# 手动分析交织器的逻辑
test_input = torch.randn(2, 64, 128)
print(f"测试输入形状: {test_input.shape}")

# 交织步骤
orig_shape = test_input.shape
batch_size = orig_shape[0]
print(f"原始形状: {orig_shape}, batch_size: {batch_size}")

# 交换batch和channel维度
x_interleaved = test_input.permute(1, 0, 2)  # [64, 2, 128]
print(f"交换维度后: {x_interleaved.shape}")

x_interleaved_shape = x_interleaved.shape
x_interleaved_flat = x_interleaved.reshape(batch_size, -1)  # [2, 8192]
print(f"展平后: {x_interleaved_flat.shape}")

metadata = {"x_interleaved_shape": x_interleaved_shape}
print(f"metadata: {metadata}")

# 解交织步骤
x_restored = x_interleaved_flat.reshape(x_interleaved_shape)  # [64, 2, 128]
print(f"恢复形状后: {x_restored.shape}")

x_final = x_restored.permute(1, 0, 2)  # [2, 64, 128]
print(f"最终形状: {x_final.shape}")

if torch.equal(test_input, x_final):
    print("✓ 手动交织/解交织成功")
else:
    print("✗ 手动交织/解交织失败")
