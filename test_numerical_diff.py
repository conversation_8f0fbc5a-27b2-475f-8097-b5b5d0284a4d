import torch
from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer
import argparse

# 创建简单的参数
class Args:
    def __init__(self):
        self.model_path = 'results/my_experiment_phase3/models/celeba_phase3_epoch5_snr5.pth'
        self.trainset = 'celeba'
        self.testset = 'kodak'
        self.data_path = './data'
        self.model = 'WITT_W/O'
        self.C = 512
        self.quant_code_dim = 128
        self.channel_type = 'rayleigh'
        self.multiple_snr = '5'
        self.distortion_metric = 'MSE'
        self.experiment_name = 'default_exp'
        self.num_workers = 0

args = Args()
cfg = BaseConfig(args)

# 创建模型实例
net1 = CoreModel(args, cfg).to(cfg.device)
quantizer = DENSEQuantizer(
    feature_dim=cfg.encoder_kwargs['embed_dims'][-1], code_dim=cfg.quant_code_dim
).to(cfg.device)
interleaver = BlockInterleaver().to(cfg.device)

# 完整的正确流程
def correct_pipeline(input1, input2):
    with torch.no_grad():
        # 编码
        feature1 = net1.encode(input1)
        feature2 = net1.encode(input2)
        feature = feature1 + feature2
        
        B, L_feat, D_feat = feature.shape
        
        # 量化
        quantized_feature = quantizer.quantize_features_to_bits(feature.reshape(B * L_feat, D_feat))
        quantized_feature = quantized_feature.reshape(B, L_feat, cfg.quant_code_dim)
        
        # 交织
        interleaved_feature, metadata = interleaver.interleave(quantized_feature)
        
        # 解交织 (使用正确的metadata)
        deinterleaved_feature = interleaver.deinterleave(interleaved_feature, metadata)
        
        # 反量化
        deinterleaved_feature_flat = deinterleaved_feature.reshape(B * L_feat, cfg.quant_code_dim)
        dequantized_feature = quantizer.dequantize_bits_to_features(deinterleaved_feature_flat)
        dequantized_feature = dequantized_feature.reshape(B, L_feat, -1)
        
        # 解码
        recon_image1 = net1.decode(dequantized_feature)
        recon_image2 = net1.decode(dequantized_feature)
        
        return recon_image1, recon_image2, interleaved_feature

# 模拟ONNX接收端的错误流程
def onnx_pipeline(interleaved_feature):
    with torch.no_grad():
        B, L = interleaved_feature.shape
        
        # 这是convert_to_onnx.py中的问题代码
        metadata = {"x_interleaved_shape": torch.Size([64, B, L // 64])}
        
        # 解交织
        deinterleaved_feature = interleaver.deinterleave(interleaved_feature, metadata)
        
        # 反量化 (使用硬编码的64)
        deinterleaved_feature_flat = deinterleaved_feature.reshape(B * 64, cfg.quant_code_dim)
        dequantized_feature = quantizer.dequantize_bits_to_features(deinterleaved_feature_flat)
        dequantized_feature = dequantized_feature.reshape(B, 64, -1)
        
        # 解码
        recon_image1 = net1.decode(dequantized_feature)
        recon_image2 = net1.decode(dequantized_feature)
        
        return recon_image1, recon_image2

# 测试不同batch size
for batch_size in [1, 2, 4]:
    print(f"\n=== 测试 batch_size = {batch_size} ===")
    
    # 创建测试输入
    dummy_input1 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
    dummy_input2 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
    
    # 正确的流程
    correct_recon1, correct_recon2, interleaved = correct_pipeline(dummy_input1, dummy_input2)
    
    # ONNX模拟流程
    onnx_recon1, onnx_recon2 = onnx_pipeline(interleaved)
    
    # 计算差异
    diff1 = torch.max(torch.abs(correct_recon1 - onnx_recon1))
    diff2 = torch.max(torch.abs(correct_recon2 - onnx_recon2))
    
    print(f"重建图像1最大差异: {diff1.item():.6f}")
    print(f"重建图像2最大差异: {diff2.item():.6f}")
    
    # 计算MSE
    mse1 = torch.mean((correct_recon1 - onnx_recon1) ** 2)
    mse2 = torch.mean((correct_recon2 - onnx_recon2) ** 2)
    
    print(f"重建图像1 MSE: {mse1.item():.8f}")
    print(f"重建图像2 MSE: {mse2.item():.8f}")
    
    # 检查是否完全相同
    if torch.equal(correct_recon1, onnx_recon1) and torch.equal(correct_recon2, onnx_recon2):
        print("✓ 结果完全相同")
    else:
        print("✗ 结果不同")
        
        # 检查形状是否相同
        if correct_recon1.shape == onnx_recon1.shape:
            print("  形状相同，但数值不同")
        else:
            print(f"  形状不同: {correct_recon1.shape} vs {onnx_recon1.shape}")

print("\n=== 深入分析 ===")
print("如果在64x64图像下结果相同，问题可能在于:")
print("1. 不同图像尺寸下的处理")
print("2. ONNX转换过程中的精度损失")
print("3. 动态batch处理的问题")
print("4. 交织器在ONNX中的行为差异")
