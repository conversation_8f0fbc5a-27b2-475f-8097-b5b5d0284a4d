import argparse

import onnx
import torch
import torch.onnx

from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer
from utils import load_individual_model_state


def parse_args():
    parser = argparse.ArgumentParser(description="Convert WITT model to ONNX")
    # 模型加载相关参数
    parser.add_argument(
        "--model_path",
        type=str,
        default="results/my_experiment_phase3/models/celeba_phase3_epoch5_snr5.pth",
        help="Path to the trained model checkpoint",
    )

    # 数据集相关参数
    parser.add_argument(
        "--trainset",
        type=str,
        default="celeba",
        choices=["CIFAR10", "DIV2K", "celeba", "tinyimagenet"],
        help="Dataset used for training",
    )
    parser.add_argument(
        "--testset",
        type=str,
        default="kodak",
        choices=["kodak", "CLIC21"],
        help="Test dataset name",
    )
    parser.add_argument(
        "--data_path", type=str, default="./data", help="Path to the dataset"
    )

    # 模型相关参数
    parser.add_argument(
        "--model",
        type=str,
        default="WITT_W/O",
        choices=["WITT_W/O"],
        help="Model type",
    )
    parser.add_argument("--C", type=int, default=512, help="Bottleneck dimension")
    parser.add_argument("--quant_code_dim", type=int, default=128)

    # 通道相关参数
    parser.add_argument(
        "--channel_type",
        type=str,
        default="rayleigh",
        choices=["awgn", "rayleigh"],
        help="Channel type",
    )
    parser.add_argument(
        "--multiple_snr", type=str, default="5", help="SNR values for training"
    )

    # 损失函数相关参数
    parser.add_argument(
        "--distortion_metric",
        type=str,
        default="MSE",
        choices=["MSE", "MS-SSIM"],
        help="Distortion metric for evaluation",
    )

    # 实验相关参数
    parser.add_argument(
        "--experiment_name",
        type=str,
        default="default_exp",
        help="Name of the experiment",
    )
    parser.add_argument(
        "--num_workers", type=int, default=0, help="Number of workers for data loading"
    )
    return parser.parse_args()


def main():
    # 解析命令行参数
    args = parse_args()

    # 初始化配置
    cfg = BaseConfig(args)

    # 创建模型实例
    net1 = CoreModel(args, cfg).to(cfg.device)
    net2 = CoreModel(args, cfg).to(cfg.device)
    quantizer = DENSEQuantizer(
        feature_dim=cfg.encoder_kwargs["embed_dims"][-1], code_dim=cfg.quant_code_dim
    ).to(cfg.device)
    interleaver = BlockInterleaver().to(cfg.device)

    # 加载模型权重
    checkpoint = torch.load(args.model_path)
    print("Checkpoint keys:", checkpoint.keys())
    load_individual_model_state(net1, checkpoint["net1_state_dict"], cfg.device)
    load_individual_model_state(net2, checkpoint["net2_state_dict"], cfg.device)
    load_individual_model_state(
        quantizer, checkpoint["quantizer_state_dict"], cfg.device
    )

    # 设置为评估模式
    net1.eval()
    net2.eval()
    quantizer.eval()
    interleaver.eval()

    # 创建虚拟输入 - 使用正确的数据范围 [0, 1]
    batch_size = 1
    dummy_input1 = torch.rand(
        batch_size, 3, cfg.image_dims[1], cfg.image_dims[2], requires_grad=True
    ).to(cfg.device)
    dummy_input2 = torch.rand(
        batch_size, 3, cfg.image_dims[1], cfg.image_dims[2], requires_grad=True
    ).to(cfg.device)

    # 定义发送端模型
    class TransmitterModel(torch.nn.Module):
        def __init__(self, net1, net2, quantizer, interleaver):
            super().__init__()
            self.net1 = net1
            self.net2 = net2
            self.quantizer = quantizer
            self.interleaver = interleaver

        def forward(self, input1, input2):
            # 编码 - WITT_W/O模型不需要SNR参数
            feature1 = self.net1.encode(input1)
            feature2 = self.net2.encode(input2)
            feature = feature1 + feature2
            B1, L1_feat, D1_feat = feature1.shape
            B2, L2_feat, D2_feat = feature2.shape
            # 量化
            quantized_feature = self.quantizer.quantize_features_to_bits(
                feature.reshape(B1 * L1_feat, D1_feat)
            )
            quantized_feature = quantized_feature.reshape(
                B1, L1_feat, cfg.quant_code_dim
            )
            # 交织
            interleaved_feature, metadata = self.interleaver.interleave(
                quantized_feature
            )
            return interleaved_feature

    # 定义接收端模型
    class ReceiverModel(torch.nn.Module):
        def __init__(self, net1, net2, quantizer, interleaver):
            super().__init__()
            self.net1 = net1
            self.net2 = net2
            self.quantizer = quantizer
            self.interleaver = interleaver

        def forward(self, interleaved_feature):
            # 动态计算正确的metadata，而不是硬编码
            B, L = interleaved_feature.shape
            # 计算L_feat: L = L_feat * quant_code_dim
            L_feat = L // cfg.quant_code_dim

            # 构建正确的metadata
            metadata = {
                "x_interleaved_shape": torch.Size([L_feat, B, cfg.quant_code_dim])
            }

            # 解交织
            deinterleaved_feature = self.interleaver.deinterleave(
                interleaved_feature, metadata
            )
            # 使用动态计算的L_feat而不是硬编码的64
            deinterleaved_feature = deinterleaved_feature.reshape(
                B * L_feat, cfg.quant_code_dim
            )
            # 反量化
            dequantized_feature = self.quantizer.dequantize_bits_to_features(
                deinterleaved_feature
            ).reshape(B, L_feat, -1)

            # 在ONNX模型中，我们简化处理：两个网络都解码同一个特征
            # 这与训练时略有不同（训练时两个路径有不同的噪声），
            # 但对于推理来说是合理的简化
            recon_image1 = self.net1.decode(dequantized_feature)
            recon_image2 = self.net2.decode(dequantized_feature)
            return recon_image1, recon_image2

    # 创建发送端和接收端模型
    transmitter_model = TransmitterModel(net1, net2, quantizer, interleaver)
    receiver_model = ReceiverModel(net1, net2, quantizer, interleaver)

    # 导出发送端模型
    torch.onnx.export(
        transmitter_model,
        (dummy_input1, dummy_input2),
        "transmitter_model.onnx",
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=["input1", "input2"],
        output_names=["interleaved_feature"],
        dynamic_axes={
            "input1": {0: "batch_size"},
            "input2": {0: "batch_size"},
            "interleaved_feature": {0: "batch_size"},
        },
    )

    # 导出接收端模型
    # 首先运行发送端模型获取实际的输出维度
    with torch.no_grad():
        test_output = transmitter_model(dummy_input1, dummy_input2)
        actual_interleaved_dim = test_output.shape[1]

    print(f"实际交织特征维度: {actual_interleaved_dim}")

    dummy_interleaved = torch.randn(
        batch_size, actual_interleaved_dim, requires_grad=True
    ).to(cfg.device)

    torch.onnx.export(
        receiver_model,
        (dummy_interleaved,),
        "receiver_model.onnx",
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=["interleaved_feature"],
        output_names=["output1", "output2"],
        dynamic_axes={
            "interleaved_feature": {0: "batch_size"},
            "output1": {0: "batch_size"},
            "output2": {0: "batch_size"},
        },
    )

    # 验证模型
    try:
        onnx.checker.check_model("transmitter_model.onnx")
        print("发送端模型验证成功!")
    except onnx.checker.ValidationError as e:
        print("发送端模型验证失败: %s" % e)

    try:
        onnx.checker.check_model("receiver_model.onnx")
        print("接收端模型验证成功!")
    except onnx.checker.ValidationError as e:
        print("接收端模型验证失败: %s" % e)


if __name__ == "__main__":
    main()
