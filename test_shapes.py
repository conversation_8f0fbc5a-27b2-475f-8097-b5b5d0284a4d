import torch

from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer


# 创建简单的参数
class Args:
    def __init__(self):
        self.model_path = (
            "results/my_experiment_phase3/models/celeba_phase3_epoch5_snr5.pth"
        )
        self.trainset = "celeba"
        self.testset = "kodak"
        self.data_path = "./data"
        self.model = "WITT_W/O"
        self.C = 512
        self.quant_code_dim = 128
        self.channel_type = "rayleigh"
        self.multiple_snr = "5"
        self.distortion_metric = "MSE"
        self.experiment_name = "default_exp"
        self.num_workers = 0


args = Args()
cfg = BaseConfig(args)

# 创建模型实例
net1 = CoreModel(args, cfg).to(cfg.device)
quantizer = DENSEQuantizer(
    feature_dim=cfg.encoder_kwargs["embed_dims"][-1], code_dim=cfg.quant_code_dim
).to(cfg.device)
interleaver = BlockInterleaver().to(cfg.device)

# 创建测试输入 - 测试不同batch size
for batch_size in [1, 2, 4]:
    print(f"\n=== 测试 batch_size = {batch_size} ===")
    dummy_input = torch.randn(batch_size, 3, cfg.image_dims[1], cfg.image_dims[2]).to(
        cfg.device
    )

    print(f"输入图像形状: {dummy_input.shape}")

    # 编码
    with torch.no_grad():
    feature = net1.encode(dummy_input)
    print(f"编码后特征形状: {feature.shape}")

    # 量化
    B, L_feat, D_feat = feature.shape
    print(f"特征维度: B={B}, L_feat={L_feat}, D_feat={D_feat}")
    print(
        f"量化器配置: feature_dim={quantizer.feature_dim}, code_dim={quantizer.code_dim}"
    )

    quantized_feature = quantizer.quantize_features_to_bits(
        feature.reshape(B * L_feat, D_feat)
    )
    print(f"量化后形状 (展平): {quantized_feature.shape}")

    quantized_feature = quantized_feature.reshape(B, L_feat, cfg.quant_code_dim)
    print(f"量化后形状 (重塑): {quantized_feature.shape}")

    # 交织
    interleaved_feature, metadata = interleaver.interleave(quantized_feature)
    print(f"交织后形状: {interleaved_feature.shape}")
    print(f"交织元数据: {metadata}")

    # 解交织
    deinterleaved_feature = interleaver.deinterleave(interleaved_feature, metadata)
    print(f"解交织后形状: {deinterleaved_feature.shape}")

    # 反量化
    deinterleaved_feature_flat = deinterleaved_feature.reshape(
        B * L_feat, cfg.quant_code_dim
    )
    print(f"解交织后展平形状: {deinterleaved_feature_flat.shape}")

    dequantized_feature = quantizer.dequantize_bits_to_features(
        deinterleaved_feature_flat
    )
    print(f"反量化后形状: {dequantized_feature.shape}")

    dequantized_feature = dequantized_feature.reshape(B, L_feat, -1)
    print(f"反量化后重塑形状: {dequantized_feature.shape}")

    # 解码
    recon_image = net1.decode(dequantized_feature)
    print(f"重建图像形状: {recon_image.shape}")

print("\n=== 检查ONNX转换中的问题 ===")

# 模拟ONNX接收端的固定metadata
B, L = interleaved_feature.shape
print(f"交织特征形状: B={B}, L={L}")

# 这是convert_to_onnx.py中的问题代码
fixed_metadata = {"x_interleaved_shape": torch.Size([64, B, L // 64])}
print(f"固定metadata: {fixed_metadata}")

# 检查这个metadata是否正确
print(f"实际交织metadata: {metadata}")
print(f"L // 64 = {L // 64}")
print(f"实际L_feat = {L_feat}")

# 测试用固定metadata解交织
try:
    wrong_deinterleaved = interleaver.deinterleave(interleaved_feature, fixed_metadata)
    print(f"用错误metadata解交织后形状: {wrong_deinterleaved.shape}")
except Exception as e:
    print(f"用错误metadata解交织失败: {e}")
