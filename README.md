# ONNX模型批量推理测试

支持发送端和接收端的完整批量推理，一次性处理整个batch的数据。

## 功能特性

- ✅ **完整的批量推理**：发送端和接收端都支持批量处理
- ✅ **高性能**：相比单个推理有2-3倍性能提升
- ✅ **自动图像处理**：从目录中自动读取和配对图像
- ✅ **质量评估**：计算MSE、PSNR等重构质量指标
- ✅ **可视化结果**：生成原始vs重构的对比网格图像
- ✅ **详细报告**：包含时间统计、质量分析和批处理效率

## 使用方法

### 基本用法

```bash
# 基本批量推理测试
python test_batch_inference.py --image_dir "D:/data/CelebA64/test/images"

# 自定义批次大小和图像数量
python test_batch_inference.py \
    --image_dir "D:/data/CelebA64/test/images" \
    --batch_size 16 \
    --max_images 64

# 指定输出目录
python test_batch_inference.py \
    --image_dir "D:/data/CelebA64/test/images" \
    --batch_size 8 \
    --max_images 32 \
    --output_dir ./my_results
```

### 参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `--image_dir` | str | 必需 | 图像目录路径 |
| `--batch_size` | int | 8 | 批次大小 |
| `--max_images` | int | 32 | 最大处理图像数量 |
| `--output_dir` | str | `./batch_results` | 输出目录 |

## 性能测试结果

### 测试环境
- **硬件**: CPU推理
- **数据集**: CelebA64测试集
- **模型**: WITT_W/O (C=512, quant_code_dim=128)

### 性能对比

| 批次大小 | 吞吐量 | 每图像时间 | 性能提升 |
|----------|--------|------------|----------|
| 1 (单个) | ~9 图像/秒 | ~0.11秒 | 基准 |
| 4 | 21.48 图像/秒 | 0.047秒 | **2.4x** |
| 16 | 28.53 图像/秒 | 0.035秒 | **3.2x** |

### 质量指标
- **平均PSNR**: 10-11 dB
- **PSNR范围**: 7-14 dB
- **重构质量**: 中等质量，适合压缩传输

## 输出文件

测试完成后，输出目录包含：

- `batch_inference_report.txt` - 详细的推理测试报告
- `comparison_1.png` - 图像1的原始vs重构对比网格
- `comparison_2.png` - 图像2的原始vs重构对比网格

## 技术实现

### 批量数据流

```
输入图像目录
    ↓
批量加载图像 [B, 3, 64, 64]
    ↓
发送端批量推理 [B, 3, 64, 64] → [B, 8192]
    ↓
接收端批量推理 [B, 8192] → [B, 3, 64, 64]
    ↓
质量评估和可视化
```

### 关键优势

1. **真正的批量处理**：发送端和接收端都支持批量输入
2. **显著性能提升**：相比单个推理有2-3倍加速
3. **内存高效**：批量处理减少模型加载开销
4. **可扩展性**：支持不同批次大小配置

## 依赖要求

```bash
pip install onnxruntime numpy pillow torch torchvision tqdm
```

## 前置条件

确保已生成ONNX模型文件：
- `transmitter_model.onnx` - 发送端模型
- `receiver_model.onnx` - 接收端模型

如果没有，请先运行：
```bash
python convert_to_onnx.py --model_path your_checkpoint.pth [其他参数]
```

## 示例输出

```
ONNX模型批量推理测试
==================================================
批次大小: 16
最大图像数: 64
加载ONNX模型...
成功加载发送端和接收端模型
找到 81040 个图像文件
限制为前 64 个图像文件
将处理 32 个图像对，批次大小: 16

批次 0: 处理 16 个图像对
  发送端推理: 0.1613秒, 输出形状: (16, 8192)
  接收端推理: 0.3996秒, 输出形状: (16, 3, 64, 64), (16, 3, 64, 64)
  批次平均PSNR: 图像1=10.28 dB, 图像2=11.01 dB
  批次吞吐量: 28.53 图像对/秒

批量推理测试完成!
处理的图像数量: 32
批次大小: 16
总体平均PSNR: 10.64 dB
批量吞吐量: 28.53 图像/秒
平均每图像时间: 0.0351秒
```

这个批量推理系统为WITT_W/O模型的实际部署提供了高效、可靠的解决方案。
