import torch
from models.interleaver import BlockInterleaver

print("=== 测试交织器行为 ===")

interleaver = BlockInterleaver()

# 测试不同batch size
for batch_size in [1, 2, 4]:
    print(f"\n--- batch_size = {batch_size} ---")
    
    # 创建测试数据 (模拟量化后的特征)
    L_feat = 64
    code_dim = 128
    test_input = torch.randn(batch_size, L_feat, code_dim)
    print(f"输入形状: {test_input.shape}")
    
    # 交织
    interleaved, metadata = interleaver.interleave(test_input)
    print(f"交织后形状: {interleaved.shape}")
    print(f"正确metadata: {metadata}")
    
    # 正确的解交织
    correct_deinterleaved = interleaver.deinterleave(interleaved, metadata)
    print(f"正确解交织后形状: {correct_deinterleaved.shape}")
    
    # 检查是否完全恢复
    if torch.equal(test_input, correct_deinterleaved):
        print("✓ 正确的交织/解交织完全恢复")
    else:
        diff = torch.max(torch.abs(test_input - correct_deinterleaved))
        print(f"✗ 正确的交织/解交织有差异: {diff}")
    
    # 模拟convert_to_onnx.py中的错误metadata
    B_inter, L_inter = interleaved.shape
    wrong_metadata = {"x_interleaved_shape": torch.Size([64, B_inter, L_inter // 64])}
    print(f"错误metadata: {wrong_metadata}")
    
    # 用错误metadata解交织
    try:
        wrong_deinterleaved = interleaver.deinterleave(interleaved, wrong_metadata)
        print(f"错误解交织后形状: {wrong_deinterleaved.shape}")
        
        # 检查差异
        if torch.equal(test_input, wrong_deinterleaved):
            print("✓ 错误metadata竟然也产生了正确结果")
        else:
            diff = torch.max(torch.abs(test_input - wrong_deinterleaved))
            print(f"✗ 错误metadata产生差异: {diff}")
            
            # 检查形状是否匹配
            if test_input.shape == wrong_deinterleaved.shape:
                print("  形状匹配但数值不同")
                # 显示一些具体的差异
                print(f"  原始前3个元素: {test_input.flatten()[:3]}")
                print(f"  错误解交织前3个元素: {wrong_deinterleaved.flatten()[:3]}")
            else:
                print(f"  形状不匹配: {test_input.shape} vs {wrong_deinterleaved.shape}")
            
    except Exception as e:
        print(f"错误metadata解交织失败: {e}")

print("\n=== 分析问题 ===")
print("如果错误metadata在某些情况下产生正确结果，")
print("那么问题可能在于:")
print("1. 特定的L_feat=64时，错误metadata恰好正确")
print("2. 交织器的实现对某些形状是对称的")
print("3. ONNX转换过程中的其他问题")

print("\n=== 测试不同L_feat ===")
for L_feat in [32, 64, 128]:
    print(f"\n--- L_feat = {L_feat} ---")
    batch_size = 2
    code_dim = 128
    
    test_input = torch.randn(batch_size, L_feat, code_dim)
    print(f"输入形状: {test_input.shape}")
    
    # 交织
    interleaved, metadata = interleaver.interleave(test_input)
    print(f"交织后形状: {interleaved.shape}")
    print(f"正确metadata: {metadata}")
    
    # 错误metadata (假设L_feat=64)
    B_inter, L_inter = interleaved.shape
    if L_inter % 64 == 0:
        wrong_metadata = {"x_interleaved_shape": torch.Size([64, B_inter, L_inter // 64])}
        print(f"错误metadata: {wrong_metadata}")
        
        try:
            wrong_deinterleaved = interleaver.deinterleave(interleaved, wrong_metadata)
            print(f"错误解交织后形状: {wrong_deinterleaved.shape}")
            
            if torch.equal(test_input, wrong_deinterleaved):
                print("✓ 错误metadata产生了正确结果")
            else:
                diff = torch.max(torch.abs(test_input - wrong_deinterleaved))
                print(f"✗ 错误metadata产生差异: {diff}")
        except Exception as e:
            print(f"错误metadata解交织失败: {e}")
    else:
        print(f"L_inter ({L_inter}) 不能被64整除，跳过错误metadata测试")
