import torch
from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer
import argparse

# 创建简单的参数
class Args:
    def __init__(self):
        self.model_path = 'results/my_experiment_phase3/models/celeba_phase3_epoch5_snr5.pth'
        self.trainset = 'celeba'
        self.testset = 'kodak'
        self.data_path = './data'
        self.model = 'WITT_W/O'
        self.C = 512
        self.quant_code_dim = 128
        self.channel_type = 'rayleigh'
        self.multiple_snr = '5'
        self.distortion_metric = 'MSE'
        self.experiment_name = 'default_exp'
        self.num_workers = 0

args = Args()
cfg = BaseConfig(args)

# 创建模型实例
net1 = CoreModel(args, cfg).to(cfg.device)
quantizer = DENSEQuantizer(
    feature_dim=cfg.encoder_kwargs['embed_dims'][-1], code_dim=cfg.quant_code_dim
).to(cfg.device)
interleaver = BlockInterleaver().to(cfg.device)

# 测试不同图像尺寸
test_sizes = [(64, 64), (128, 128), (256, 256)]
batch_size = 1

for h, w in test_sizes:
    print(f"\n=== 测试图像尺寸 {h}x{w} ===")
    dummy_input = torch.randn(batch_size, 3, h, w).to(cfg.device)
    print(f"输入图像形状: {dummy_input.shape}")

    # 编码
    with torch.no_grad():
        try:
            feature = net1.encode(dummy_input)
            print(f"编码后特征形状: {feature.shape}")
            
            # 量化
            B, L_feat, D_feat = feature.shape
            print(f"特征维度: B={B}, L_feat={L_feat}, D_feat={D_feat}")
            
            quantized_feature = quantizer.quantize_features_to_bits(feature.reshape(B * L_feat, D_feat))
            quantized_feature = quantized_feature.reshape(B, L_feat, cfg.quant_code_dim)
            print(f"量化后形状: {quantized_feature.shape}")
            
            # 交织
            interleaved_feature, metadata = interleaver.interleave(quantized_feature)
            print(f"交织后形状: {interleaved_feature.shape}")
            print(f"实际交织metadata: {metadata}")
            
            # 模拟ONNX接收端的固定metadata (假设L_feat=64)
            B_inter, L_inter = interleaved_feature.shape
            fixed_metadata = {"x_interleaved_shape": torch.Size([64, B_inter, L_inter // 64])}
            print(f"固定metadata (假设L_feat=64): {fixed_metadata}")
            
            # 检查固定metadata是否正确
            print(f"L_inter // 64 = {L_inter // 64}, 实际L_feat = {L_feat}")
            
            if L_inter % 64 != 0:
                print(f"⚠️  警告: L_inter ({L_inter}) 不能被64整除!")
            
            # 测试用固定metadata解交织
            try:
                # 正确的解交织
                correct_deinterleaved = interleaver.deinterleave(interleaved_feature, metadata)
                
                # 用固定metadata解交织
                fixed_deinterleaved = interleaver.deinterleave(interleaved_feature, fixed_metadata)
                
                print(f"正确解交织后形状: {correct_deinterleaved.shape}")
                print(f"固定metadata解交织后形状: {fixed_deinterleaved.shape}")
                
                # 检查是否相同
                if torch.equal(correct_deinterleaved, fixed_deinterleaved):
                    print("✓ 固定metadata产生了正确的结果")
                else:
                    print("✗ 固定metadata产生了错误的结果")
                    diff = torch.max(torch.abs(correct_deinterleaved - fixed_deinterleaved))
                    print(f"最大差异: {diff}")
                    
                    # 继续测试反量化和解码的影响
                    try:
                        correct_flat = correct_deinterleaved.reshape(B * L_feat, cfg.quant_code_dim)
                        fixed_flat = fixed_deinterleaved.reshape(B * L_feat, cfg.quant_code_dim)
                        
                        correct_dequant = quantizer.dequantize_bits_to_features(correct_flat).reshape(B, L_feat, -1)
                        fixed_dequant = quantizer.dequantize_bits_to_features(fixed_flat).reshape(B, L_feat, -1)
                        
                        correct_recon = net1.decode(correct_dequant)
                        fixed_recon = net1.decode(fixed_dequant)
                        
                        recon_diff = torch.max(torch.abs(correct_recon - fixed_recon))
                        print(f"重建图像最大差异: {recon_diff}")
                    except Exception as e:
                        print(f"后续处理失败: {e}")
                    
            except Exception as e:
                print(f"用固定metadata解交织失败: {e}")
                
        except Exception as e:
            print(f"编码失败: {e}")

print("\n=== 总结 ===")
print("如果在不同图像尺寸下L_feat不同，固定metadata就会出错")
print("这可能是ONNX推理结果差的原因")
