import torch
from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer
import argparse

# 创建简单的参数
class Args:
    def __init__(self):
        self.model_path = 'results/my_experiment_phase3/models/celeba_phase3_epoch5_snr5.pth'
        self.trainset = 'celeba'
        self.testset = 'kodak'
        self.data_path = './data'
        self.model = 'WITT_W/O'
        self.C = 512
        self.quant_code_dim = 128
        self.channel_type = 'rayleigh'
        self.multiple_snr = '5'
        self.distortion_metric = 'MSE'
        self.experiment_name = 'default_exp'
        self.num_workers = 0

args = Args()
cfg = BaseConfig(args)

# 创建模型实例
net1 = CoreModel(args, cfg).to(cfg.device)
net2 = CoreModel(args, cfg).to(cfg.device)
quantizer = DENSEQuantizer(
    feature_dim=cfg.encoder_kwargs['embed_dims'][-1], code_dim=cfg.quant_code_dim
).to(cfg.device)
interleaver = BlockInterleaver().to(cfg.device)

print("=== 调试解码器差异 ===")

batch_size = 1
dummy_input1 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
dummy_input2 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)

with torch.no_grad():
    # 完整流程到反量化
    feature1 = net1.encode(dummy_input1)
    feature2 = net2.encode(dummy_input2)
    feature = feature1 + feature2
    
    B, L_feat, D_feat = feature.shape
    feature_flat = feature.reshape(B * L_feat, D_feat)
    quantized_feature_flat = quantizer.quantize_features_to_bits(feature_flat)
    quantized_feature = quantized_feature_flat.reshape(B, L_feat, cfg.quant_code_dim)
    
    interleaved_feature, metadata = interleaver.interleave(quantized_feature)
    deinterleaved_feature = interleaver.deinterleave(interleaved_feature, metadata)
    
    deinterleaved_flat = deinterleaved_feature.reshape(B * L_feat, cfg.quant_code_dim)
    dequantized_flat = quantizer.dequantize_bits_to_features(deinterleaved_flat)
    dequantized_feature = dequantized_flat.reshape(B, L_feat, -1)
    
    print(f"反量化后特征形状: {dequantized_feature.shape}")
    print(f"反量化后特征前5个元素: {dequantized_feature.flatten()[:5]}")
    
    # 测试解码器的一致性
    print("\n=== 测试解码器一致性 ===")
    
    # 多次解码同一个特征
    recon1_1 = net1.decode(dequantized_feature)
    recon1_2 = net1.decode(dequantized_feature)
    recon2_1 = net2.decode(dequantized_feature)
    recon2_2 = net2.decode(dequantized_feature)
    
    print(f"net1第一次解码前5个元素: {recon1_1.flatten()[:5]}")
    print(f"net1第二次解码前5个元素: {recon1_2.flatten()[:5]}")
    print(f"net2第一次解码前5个元素: {recon2_1.flatten()[:5]}")
    print(f"net2第二次解码前5个元素: {recon2_2.flatten()[:5]}")
    
    # 检查同一网络的一致性
    if torch.equal(recon1_1, recon1_2):
        print("✓ net1解码结果一致")
    else:
        diff = torch.max(torch.abs(recon1_1 - recon1_2))
        print(f"✗ net1解码结果不一致，差异: {diff}")
    
    if torch.equal(recon2_1, recon2_2):
        print("✓ net2解码结果一致")
    else:
        diff = torch.max(torch.abs(recon2_1 - recon2_2))
        print(f"✗ net2解码结果不一致，差异: {diff}")
    
    # 检查两个网络的差异
    if torch.equal(recon1_1, recon2_1):
        print("✓ net1和net2解码结果相同")
    else:
        diff = torch.max(torch.abs(recon1_1 - recon2_1))
        print(f"✗ net1和net2解码结果不同，差异: {diff}")
    
    print("\n=== 检查网络权重 ===")
    # 检查两个网络的权重是否相同
    net1_params = list(net1.parameters())
    net2_params = list(net2.parameters())
    
    if len(net1_params) != len(net2_params):
        print(f"✗ 网络参数数量不同: {len(net1_params)} vs {len(net2_params)}")
    else:
        all_same = True
        for i, (p1, p2) in enumerate(zip(net1_params, net2_params)):
            if not torch.equal(p1, p2):
                diff = torch.max(torch.abs(p1 - p2))
                print(f"✗ 参数{i}不同，差异: {diff}")
                all_same = False
                break
        
        if all_same:
            print("✓ 所有网络参数相同")
    
    print("\n=== 检查网络状态 ===")
    print(f"net1训练模式: {net1.training}")
    print(f"net2训练模式: {net2.training}")
    
    # 确保都在评估模式
    net1.eval()
    net2.eval()
    
    # 再次测试
    recon1_eval = net1.decode(dequantized_feature)
    recon2_eval = net2.decode(dequantized_feature)
    
    if torch.equal(recon1_eval, recon2_eval):
        print("✓ 评估模式下net1和net2解码结果相同")
    else:
        diff = torch.max(torch.abs(recon1_eval - recon2_eval))
        print(f"✗ 评估模式下net1和net2解码结果仍不同，差异: {diff}")
    
    print("\n=== 检查解码器内部状态 ===")
    # 检查解码器的分辨率设置
    print(f"net1解码器目标分辨率: {net1.decoder.target_H}x{net1.decoder.target_W}")
    print(f"net2解码器目标分辨率: {net2.decoder.target_H}x{net2.decoder.target_W}")
    print(f"net1解码器初始分辨率: {net1.decoder.initial_H}x{net1.decoder.initial_W}")
    print(f"net2解码器初始分辨率: {net2.decoder.initial_H}x{net2.decoder.initial_W}")

print("\n=== 结论 ===")
print("如果两个网络权重相同但输出不同，可能的原因:")
print("1. 网络内部状态不同（如BatchNorm的running stats）")
print("2. 解码器的分辨率设置不同")
print("3. 随机性（如Dropout，但应该在eval模式下关闭）")
print("4. 网络初始化时的差异")
