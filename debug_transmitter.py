import torch
import numpy as np
import onnxruntime as ort
from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer
from utils import load_individual_model_state
import argparse

# 创建简单的参数
class Args:
    def __init__(self):
        self.model_path = 'results/my_experiment_phase3/models/celeba_phase3_epoch5_snr5.pth'
        self.trainset = 'celeba'
        self.testset = 'kodak'
        self.data_path = './data'
        self.model = 'WITT_W/O'
        self.C = 512
        self.quant_code_dim = 128
        self.channel_type = 'rayleigh'
        self.multiple_snr = '5'
        self.distortion_metric = 'MSE'
        self.experiment_name = 'default_exp'
        self.num_workers = 0

def main():
    args = Args()
    cfg = BaseConfig(args)
    
    print("=== 调试发送端模型转换问题 ===")
    
    # 1. 加载 PyTorch 模型
    print("1. 加载 PyTorch 模型...")
    net1 = CoreModel(args, cfg).to(cfg.device)
    net2 = CoreModel(args, cfg).to(cfg.device)
    quantizer = DENSEQuantizer(
        feature_dim=cfg.encoder_kwargs['embed_dims'][-1], code_dim=cfg.quant_code_dim
    ).to(cfg.device)
    interleaver = BlockInterleaver().to(cfg.device)
    
    try:
        checkpoint = torch.load(args.model_path)
        load_individual_model_state(net1, checkpoint["net1_state_dict"], cfg.device)
        load_individual_model_state(net2, checkpoint["net2_state_dict"], cfg.device)
        load_individual_model_state(quantizer, checkpoint["quantizer_state_dict"], cfg.device)
        print("✓ PyTorch 模型权重加载成功")
    except Exception as e:
        print(f"✗ PyTorch 模型权重加载失败: {e}")
        return
    
    # 设置为评估模式
    net1.eval()
    net2.eval()
    quantizer.eval()
    interleaver.eval()
    
    # 2. 加载 ONNX 模型
    print("2. 加载 ONNX 模型...")
    try:
        transmitter_session = ort.InferenceSession("transmitter_model.onnx")
        print("✓ ONNX 发送端模型加载成功")
    except Exception as e:
        print(f"✗ ONNX 发送端模型加载失败: {e}")
        return
    
    # 3. 创建测试数据
    print("3. 创建测试数据...")
    batch_size = 2
    input1 = torch.rand(batch_size, 3, 64, 64).to(cfg.device)  # [0, 1] 范围
    input2 = torch.rand(batch_size, 3, 64, 64).to(cfg.device)
    
    print(f"测试输入形状: {input1.shape}, {input2.shape}")
    print(f"输入范围: [{input1.min():.3f}, {input1.max():.3f}]")
    
    # 4. 逐步对比 PyTorch 和 ONNX 的每个步骤
    print("4. 逐步对比...")
    
    with torch.no_grad():
        # 步骤1: 编码
        print("\n--- 步骤1: 编码 ---")
        pytorch_feature1 = net1.encode(input1)
        pytorch_feature2 = net2.encode(input2)
        pytorch_feature = pytorch_feature1 + pytorch_feature2
        
        print(f"PyTorch 编码特征1形状: {pytorch_feature1.shape}")
        print(f"PyTorch 编码特征2形状: {pytorch_feature2.shape}")
        print(f"PyTorch 合并特征形状: {pytorch_feature.shape}")
        print(f"PyTorch 特征1前3个元素: {pytorch_feature1.flatten()[:3]}")
        print(f"PyTorch 特征2前3个元素: {pytorch_feature2.flatten()[:3]}")
        print(f"PyTorch 合并特征前3个元素: {pytorch_feature.flatten()[:3]}")
        
        # 步骤2: 量化
        print("\n--- 步骤2: 量化 ---")
        B, L_feat, D_feat = pytorch_feature.shape
        pytorch_feature_flat = pytorch_feature.reshape(B * L_feat, D_feat)
        pytorch_quantized_flat = quantizer.quantize_features_to_bits(pytorch_feature_flat)
        pytorch_quantized = pytorch_quantized_flat.reshape(B, L_feat, cfg.quant_code_dim)
        
        print(f"PyTorch 量化前形状: {pytorch_feature_flat.shape}")
        print(f"PyTorch 量化后形状: {pytorch_quantized_flat.shape}")
        print(f"PyTorch 量化重塑后形状: {pytorch_quantized.shape}")
        print(f"PyTorch 量化前前3个元素: {pytorch_feature_flat.flatten()[:3]}")
        print(f"PyTorch 量化后前3个元素: {pytorch_quantized_flat.flatten()[:3]}")
        
        # 步骤3: 交织
        print("\n--- 步骤3: 交织 ---")
        pytorch_interleaved, pytorch_metadata = interleaver.interleave(pytorch_quantized)
        
        print(f"PyTorch 交织前形状: {pytorch_quantized.shape}")
        print(f"PyTorch 交织后形状: {pytorch_interleaved.shape}")
        print(f"PyTorch 交织metadata: {pytorch_metadata}")
        print(f"PyTorch 交织前前3个元素: {pytorch_quantized.flatten()[:3]}")
        print(f"PyTorch 交织后前3个元素: {pytorch_interleaved.flatten()[:3]}")
    
    # 5. ONNX 发送端推理
    print("\n5. ONNX 发送端推理...")
    
    # 转换为 numpy
    input1_np = input1.cpu().numpy().astype(np.float32)
    input2_np = input2.cpu().numpy().astype(np.float32)
    
    print(f"ONNX 输入1形状: {input1_np.shape}")
    print(f"ONNX 输入2形状: {input2_np.shape}")
    print(f"ONNX 输入1前3个元素: {input1_np.flatten()[:3]}")
    print(f"ONNX 输入2前3个元素: {input2_np.flatten()[:3]}")
    
    # 发送端推理
    transmitter_outputs = transmitter_session.run(
        None, {"input1": input1_np, "input2": input2_np}
    )
    onnx_interleaved = transmitter_outputs[0]
    
    print(f"ONNX 交织后形状: {onnx_interleaved.shape}")
    print(f"ONNX 交织后前3个元素: {onnx_interleaved.flatten()[:3]}")
    
    # 6. 对比结果
    print("\n6. 对比结果...")
    
    pytorch_interleaved_np = pytorch_interleaved.cpu().numpy()
    
    # 详细对比
    print(f"PyTorch 交织结果形状: {pytorch_interleaved_np.shape}")
    print(f"ONNX 交织结果形状: {onnx_interleaved.shape}")
    
    if pytorch_interleaved_np.shape == onnx_interleaved.shape:
        print("✓ 形状匹配")
        
        # 逐元素对比
        diff = np.abs(pytorch_interleaved_np - onnx_interleaved)
        max_diff = np.max(diff)
        mean_diff = np.mean(diff)
        
        print(f"最大差异: {max_diff:.6f}")
        print(f"平均差异: {mean_diff:.6f}")
        
        # 找出差异最大的位置
        max_diff_idx = np.unravel_index(np.argmax(diff), diff.shape)
        print(f"最大差异位置: {max_diff_idx}")
        print(f"PyTorch 值: {pytorch_interleaved_np[max_diff_idx]:.6f}")
        print(f"ONNX 值: {onnx_interleaved[max_diff_idx]:.6f}")
        
        # 检查是否有系统性差异
        print(f"PyTorch 范围: [{pytorch_interleaved_np.min():.3f}, {pytorch_interleaved_np.max():.3f}]")
        print(f"ONNX 范围: [{onnx_interleaved.min():.3f}, {onnx_interleaved.max():.3f}]")
        
        # 检查前10个元素
        print("前10个元素对比:")
        for i in range(min(10, pytorch_interleaved_np.size)):
            pt_val = pytorch_interleaved_np.flatten()[i]
            onnx_val = onnx_interleaved.flatten()[i]
            diff_val = abs(pt_val - onnx_val)
            print(f"  [{i}] PyTorch: {pt_val:.6f}, ONNX: {onnx_val:.6f}, 差异: {diff_val:.6f}")
    else:
        print("✗ 形状不匹配")
    
    # 7. 分析可能的原因
    print("\n7. 可能的原因分析:")
    print("- 量化器在 ONNX 转换中的行为差异")
    print("- 交织器在 ONNX 转换中的行为差异")
    print("- 模型权重加载问题")
    print("- ONNX 转换过程中的精度损失")
    print("- 网络层的状态差异（如 BatchNorm）")

if __name__ == "__main__":
    main()
