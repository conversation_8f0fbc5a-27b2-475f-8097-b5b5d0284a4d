import torch
import numpy as np
import onnxruntime as ort
from config import BaseConfig
from models.quantization import DENSEQuantizer
from utils import load_individual_model_state
import argparse

# 创建简单的参数
class Args:
    def __init__(self):
        self.model_path = 'results/my_experiment_phase3/models/celeba_phase3_epoch5_snr5.pth'
        self.trainset = 'celeba'
        self.testset = 'kodak'
        self.data_path = './data'
        self.model = 'WITT_W/O'
        self.C = 512
        self.quant_code_dim = 128
        self.channel_type = 'rayleigh'
        self.multiple_snr = '5'
        self.distortion_metric = 'MSE'
        self.experiment_name = 'default_exp'
        self.num_workers = 0

def main():
    args = Args()
    cfg = BaseConfig(args)
    
    print("=== 简化量化器测试 ===")
    
    # 1. 创建并加载 PyTorch 量化器
    print("1. 创建并加载 PyTorch 量化器...")
    quantizer = DENSEQuantizer(
        feature_dim=cfg.encoder_kwargs['embed_dims'][-1], code_dim=cfg.quant_code_dim
    )
    
    try:
        checkpoint = torch.load(args.model_path, map_location='cpu')
        load_individual_model_state(quantizer, checkpoint["quantizer_state_dict"], 'cpu')
        print("✓ PyTorch 量化器权重加载成功")
    except Exception as e:
        print(f"✗ PyTorch 量化器权重加载失败: {e}")
        return
    
    quantizer.eval()
    
    # 2. 创建测试数据
    print("2. 创建测试数据...")
    test_features = torch.randn(2, 512)  # (batch*seq, feature_dim)
    print(f"测试特征形状: {test_features.shape}")
    print(f"测试特征前3个元素: {test_features.flatten()[:3]}")
    
    # 3. PyTorch 量化器推理
    print("3. PyTorch 量化器推理...")
    with torch.no_grad():
        pytorch_quantized = quantizer.quantize_features_to_bits(test_features)
        print(f"PyTorch 量化后形状: {pytorch_quantized.shape}")
        print(f"PyTorch 量化后前5个元素: {pytorch_quantized.flatten()[:5]}")
    
    # 4. 创建简单的量化器模型用于 ONNX 导出
    print("4. 创建简单的量化器模型...")
    
    class SimpleQuantizer(torch.nn.Module):
        def __init__(self, quantizer):
            super().__init__()
            self.feature_to_code_proj = quantizer.feature_to_code_proj
        
        def forward(self, features):
            pre_quant_code = self.feature_to_code_proj(features)
            quantized = torch.sign(pre_quant_code)
            return quantized
    
    simple_quantizer = SimpleQuantizer(quantizer)
    simple_quantizer.eval()
    
    # 5. 导出 ONNX 模型
    print("5. 导出 ONNX 模型...")
    try:
        torch.onnx.export(
            simple_quantizer,
            test_features,
            "simple_quantizer.onnx",
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=["features"],
            output_names=["quantized"],
        )
        print("✓ ONNX 导出成功")
    except Exception as e:
        print(f"✗ ONNX 导出失败: {e}")
        return
    
    # 6. 加载并测试 ONNX 模型
    print("6. 测试 ONNX 模型...")
    try:
        session = ort.InferenceSession("simple_quantizer.onnx")
        
        # 转换输入为 numpy
        test_features_np = test_features.numpy().astype(np.float32)
        
        # ONNX 推理
        onnx_result = session.run(None, {"features": test_features_np})[0]
        print(f"ONNX 量化后形状: {onnx_result.shape}")
        print(f"ONNX 量化后前5个元素: {onnx_result.flatten()[:5]}")
        
        # 比较结果
        pytorch_result_np = pytorch_quantized.numpy()
        diff = np.abs(pytorch_result_np - onnx_result)
        max_diff = np.max(diff)
        mean_diff = np.mean(diff)
        
        print(f"最大差异: {max_diff}")
        print(f"平均差异: {mean_diff}")
        
        if max_diff < 1e-6:
            print("✓ PyTorch 和 ONNX 量化结果一致")
        else:
            print("✗ PyTorch 和 ONNX 量化结果不一致")
            
            # 详细分析差异
            print("前10个元素对比:")
            for i in range(min(10, pytorch_result_np.size)):
                pt_val = pytorch_result_np.flatten()[i]
                onnx_val = onnx_result.flatten()[i]
                diff_val = abs(pt_val - onnx_val)
                print(f"  [{i}] PyTorch: {pt_val:.1f}, ONNX: {onnx_val:.1f}, 差异: {diff_val:.1f}")
                
            # 统计差异分布
            num_diff = np.sum(diff > 1e-6)
            total_elements = pytorch_result_np.size
            print(f"不同元素数量: {num_diff}/{total_elements} ({100*num_diff/total_elements:.1f}%)")
        
    except Exception as e:
        print(f"✗ ONNX 模型测试失败: {e}")

if __name__ == "__main__":
    main()
