import torch
import numpy as np
import onnxruntime as ort

print("=== 测试 torch.sign 在 ONNX 中的行为 ===")

# 创建测试数据
test_input = torch.tensor([[-2.5, -1.0, -0.1, 0.0, 0.1, 1.0, 2.5]], dtype=torch.float32)
print(f"测试输入: {test_input}")

# PyTorch 中的 sign 函数
pytorch_result = torch.sign(test_input)
print(f"PyTorch sign 结果: {pytorch_result}")

# 创建一个简单的模型来测试 sign 函数
class SimpleSignModel(torch.nn.Module):
    def forward(self, x):
        return torch.sign(x)

# 导出 ONNX 模型
model = SimpleSignModel()
model.eval()

try:
    torch.onnx.export(
        model,
        test_input,
        "test_sign.onnx",
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=["input"],
        output_names=["output"],
    )
    print("✓ ONNX 导出成功")
    
    # 加载并测试 ONNX 模型
    session = ort.InferenceSession("test_sign.onnx")
    onnx_result = session.run(None, {"input": test_input.numpy()})[0]
    print(f"ONNX sign 结果: {onnx_result}")
    
    # 比较结果
    diff = np.abs(pytorch_result.numpy() - onnx_result)
    max_diff = np.max(diff)
    print(f"最大差异: {max_diff}")
    
    if max_diff < 1e-6:
        print("✓ PyTorch 和 ONNX 结果一致")
    else:
        print("✗ PyTorch 和 ONNX 结果不一致")
        print(f"差异: {diff}")
        
except Exception as e:
    print(f"✗ ONNX 导出或运行失败: {e}")

# 测试自定义的 LBSign 函数
print("\n=== 测试自定义 LBSign 函数 ===")

class LBSign(torch.autograd.Function):
    @staticmethod
    def forward(ctx, input_tensor):
        return torch.sign(input_tensor)

    @staticmethod
    def backward(ctx, grad_output):
        return grad_output.clamp_(-1, 1)

apply_sign_ste = LBSign.apply

class CustomSignModel(torch.nn.Module):
    def forward(self, x):
        return apply_sign_ste(x)

# 测试自定义模型
custom_model = CustomSignModel()
custom_model.eval()

pytorch_custom_result = custom_model(test_input)
print(f"PyTorch 自定义 sign 结果: {pytorch_custom_result}")

try:
    torch.onnx.export(
        custom_model,
        test_input,
        "test_custom_sign.onnx",
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=["input"],
        output_names=["output"],
    )
    print("✓ 自定义 ONNX 导出成功")
    
    # 加载并测试 ONNX 模型
    custom_session = ort.InferenceSession("test_custom_sign.onnx")
    onnx_custom_result = custom_session.run(None, {"input": test_input.numpy()})[0]
    print(f"ONNX 自定义 sign 结果: {onnx_custom_result}")
    
    # 比较结果
    custom_diff = np.abs(pytorch_custom_result.numpy() - onnx_custom_result)
    max_custom_diff = np.max(custom_diff)
    print(f"最大差异: {max_custom_diff}")
    
    if max_custom_diff < 1e-6:
        print("✓ 自定义 PyTorch 和 ONNX 结果一致")
    else:
        print("✗ 自定义 PyTorch 和 ONNX 结果不一致")
        print(f"差异: {custom_diff}")
        
except Exception as e:
    print(f"✗ 自定义 ONNX 导出或运行失败: {e}")

# 测试替代方案
print("\n=== 测试替代方案 ===")

class AlternativeSignModel(torch.nn.Module):
    def forward(self, x):
        # 使用 tanh 的硬饱和版本来近似 sign 函数
        return torch.tanh(x * 1000)  # 大的系数使 tanh 接近 sign

alt_model = AlternativeSignModel()
alt_model.eval()

pytorch_alt_result = alt_model(test_input)
print(f"PyTorch 替代 sign 结果: {pytorch_alt_result}")

try:
    torch.onnx.export(
        alt_model,
        test_input,
        "test_alt_sign.onnx",
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=["input"],
        output_names=["output"],
    )
    print("✓ 替代方案 ONNX 导出成功")
    
    # 加载并测试 ONNX 模型
    alt_session = ort.InferenceSession("test_alt_sign.onnx")
    onnx_alt_result = alt_session.run(None, {"input": test_input.numpy()})[0]
    print(f"ONNX 替代 sign 结果: {onnx_alt_result}")
    
    # 比较结果
    alt_diff = np.abs(pytorch_alt_result.numpy() - onnx_alt_result)
    max_alt_diff = np.max(alt_diff)
    print(f"最大差异: {max_alt_diff}")
    
    if max_alt_diff < 1e-6:
        print("✓ 替代方案 PyTorch 和 ONNX 结果一致")
    else:
        print("✗ 替代方案 PyTorch 和 ONNX 结果不一致")
        print(f"差异: {alt_diff}")
        
except Exception as e:
    print(f"✗ 替代方案 ONNX 导出或运行失败: {e}")

print("\n=== 结论 ===")
print("如果 torch.sign 在 ONNX 中工作正常，问题可能在于:")
print("1. 自定义 autograd 函数在 ONNX 转换中的处理")
print("2. 量化器的线性层权重在 ONNX 转换中的问题")
print("3. 数值精度问题")
print("\n建议的解决方案:")
print("1. 在 ONNX 转换时直接使用 torch.sign 而不是自定义函数")
print("2. 检查量化器的权重是否正确加载到 ONNX 模型中")
