import torch
import numpy as np
import onnxruntime as ort
from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer
from utils import load_individual_model_state
import argparse

# 创建简单的参数
class Args:
    def __init__(self):
        self.model_path = 'results/my_experiment_phase3/models/celeba_phase3_epoch5_snr5.pth'
        self.trainset = 'celeba'
        self.testset = 'kodak'
        self.data_path = './data'
        self.model = 'WITT_W/O'
        self.C = 512
        self.quant_code_dim = 128
        self.channel_type = 'rayleigh'
        self.multiple_snr = '5'
        self.distortion_metric = 'MSE'
        self.experiment_name = 'default_exp'
        self.num_workers = 0

def main():
    args = Args()
    cfg = BaseConfig(args)
    
    print("=== 测试完整发送端流程 ===")
    
    # 1. 创建并加载 PyTorch 模型
    print("1. 创建并加载 PyTorch 模型...")
    net1 = CoreModel(args, cfg)
    net2 = CoreModel(args, cfg)
    quantizer = DENSEQuantizer(
        feature_dim=cfg.encoder_kwargs['embed_dims'][-1], code_dim=cfg.quant_code_dim
    )
    interleaver = BlockInterleaver()
    
    try:
        checkpoint = torch.load(args.model_path, map_location='cpu')
        load_individual_model_state(net1, checkpoint["net1_state_dict"], 'cpu')
        load_individual_model_state(net2, checkpoint["net2_state_dict"], 'cpu')
        load_individual_model_state(quantizer, checkpoint["quantizer_state_dict"], 'cpu')
        print("✓ PyTorch 模型权重加载成功")
    except Exception as e:
        print(f"✗ PyTorch 模型权重加载失败: {e}")
        return
    
    net1.eval()
    net2.eval()
    quantizer.eval()
    interleaver.eval()
    
    # 2. 创建测试数据
    print("2. 创建测试数据...")
    test_input1 = torch.rand(2, 3, 64, 64)  # [0, 1] 范围
    test_input2 = torch.rand(2, 3, 64, 64)
    print(f"测试输入形状: {test_input1.shape}, {test_input2.shape}")
    
    # 3. PyTorch 完整流程
    print("3. PyTorch 完整流程...")
    with torch.no_grad():
        # 编码
        feature1 = net1.encode(test_input1)
        feature2 = net2.encode(test_input2)
        combined_feature = feature1 + feature2
        
        B, L_feat, D_feat = combined_feature.shape
        print(f"合并特征形状: {combined_feature.shape}")
        print(f"合并特征前3个元素: {combined_feature.flatten()[:3]}")
        
        # 量化
        feature_flat = combined_feature.reshape(B * L_feat, D_feat)
        quantized_flat = quantizer.quantize_features_to_bits(feature_flat)
        quantized_feature = quantized_flat.reshape(B, L_feat, cfg.quant_code_dim)
        
        print(f"量化特征形状: {quantized_feature.shape}")
        print(f"量化特征前5个元素: {quantized_feature.flatten()[:5]}")
        
        # 交织
        interleaved_feature, metadata = interleaver.interleave(quantized_feature)
        
        print(f"交织特征形状: {interleaved_feature.shape}")
        print(f"交织特征前5个元素: {interleaved_feature.flatten()[:5]}")
        print(f"交织metadata: {metadata}")
    
    # 4. 创建完整发送端模型
    print("4. 创建完整发送端模型...")
    
    class FullTransmitterModel(torch.nn.Module):
        def __init__(self, net1, net2, quantizer, interleaver):
            super().__init__()
            self.net1 = net1
            self.net2 = net2
            self.quantizer = quantizer
            self.interleaver = interleaver

        def forward(self, input1, input2):
            # 编码
            feature1 = self.net1.encode(input1)
            feature2 = self.net2.encode(input2)
            combined_feature = feature1 + feature2
            
            B, L_feat, D_feat = combined_feature.shape
            
            # 量化
            feature_flat = combined_feature.reshape(B * L_feat, D_feat)
            quantized_flat = self.quantizer.quantize_features_to_bits(feature_flat)
            quantized_feature = quantized_flat.reshape(B, L_feat, cfg.quant_code_dim)
            
            # 交织
            interleaved_feature, metadata = self.interleaver.interleave(quantized_feature)
            
            return interleaved_feature
    
    full_transmitter = FullTransmitterModel(net1, net2, quantizer, interleaver)
    full_transmitter.eval()
    
    # 5. 导出 ONNX 模型
    print("5. 导出 ONNX 模型...")
    try:
        torch.onnx.export(
            full_transmitter,
            (test_input1, test_input2),
            "full_transmitter.onnx",
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=["input1", "input2"],
            output_names=["interleaved_feature"],
            dynamic_axes={
                "input1": {0: "batch_size"},
                "input2": {0: "batch_size"},
                "interleaved_feature": {0: "batch_size"},
            },
        )
        print("✓ ONNX 导出成功")
    except Exception as e:
        print(f"✗ ONNX 导出失败: {e}")
        return
    
    # 6. 加载并测试 ONNX 模型
    print("6. 测试 ONNX 模型...")
    try:
        session = ort.InferenceSession("full_transmitter.onnx")
        
        # 转换输入为 numpy
        input1_np = test_input1.numpy().astype(np.float32)
        input2_np = test_input2.numpy().astype(np.float32)
        
        # ONNX 推理
        onnx_result = session.run(None, {"input1": input1_np, "input2": input2_np})[0]
        print(f"ONNX 交织特征形状: {onnx_result.shape}")
        print(f"ONNX 交织特征前5个元素: {onnx_result.flatten()[:5]}")
        
        # 比较结果
        pytorch_result_np = interleaved_feature.numpy()
        diff = np.abs(pytorch_result_np - onnx_result)
        max_diff = np.max(diff)
        mean_diff = np.mean(diff)
        
        print(f"最大差异: {max_diff:.6f}")
        print(f"平均差异: {mean_diff:.6f}")
        
        if max_diff < 1e-5:
            print("✓ PyTorch 和 ONNX 发送端结果一致")
        else:
            print("✗ PyTorch 和 ONNX 发送端结果不一致")
            
            # 详细分析差异
            print("前10个元素对比:")
            for i in range(min(10, pytorch_result_np.size)):
                pt_val = pytorch_result_np.flatten()[i]
                onnx_val = onnx_result.flatten()[i]
                diff_val = abs(pt_val - onnx_val)
                print(f"  [{i}] PyTorch: {pt_val:.1f}, ONNX: {onnx_val:.1f}, 差异: {diff_val:.1f}")
                
            # 统计差异分布
            num_diff = np.sum(diff > 1e-5)
            total_elements = pytorch_result_np.size
            print(f"显著不同元素数量: {num_diff}/{total_elements} ({100*num_diff/total_elements:.1f}%)")
            
            # 检查是否是系统性差异
            print(f"PyTorch 特征范围: [{pytorch_result_np.min():.1f}, {pytorch_result_np.max():.1f}]")
            print(f"ONNX 特征范围: [{onnx_result.min():.1f}, {onnx_result.max():.1f}]")
            
            # 检查量化值的分布
            pytorch_unique, pytorch_counts = np.unique(pytorch_result_np, return_counts=True)
            onnx_unique, onnx_counts = np.unique(onnx_result, return_counts=True)
            
            print(f"PyTorch 唯一值: {pytorch_unique}")
            print(f"PyTorch 值计数: {pytorch_counts}")
            print(f"ONNX 唯一值: {onnx_unique}")
            print(f"ONNX 值计数: {onnx_counts}")
        
    except Exception as e:
        print(f"✗ ONNX 模型测试失败: {e}")
    
    # 7. 分步测试，找出问题所在
    print("\n7. 分步测试...")
    
    # 测试编码+量化（不包括交织）
    class EncodeQuantizeModel(torch.nn.Module):
        def __init__(self, net1, net2, quantizer):
            super().__init__()
            self.net1 = net1
            self.net2 = net2
            self.quantizer = quantizer

        def forward(self, input1, input2):
            feature1 = self.net1.encode(input1)
            feature2 = self.net2.encode(input2)
            combined_feature = feature1 + feature2
            
            B, L_feat, D_feat = combined_feature.shape
            feature_flat = combined_feature.reshape(B * L_feat, D_feat)
            quantized_flat = self.quantizer.quantize_features_to_bits(feature_flat)
            quantized_feature = quantized_flat.reshape(B, L_feat, cfg.quant_code_dim)
            
            return quantized_feature
    
    encode_quantize_model = EncodeQuantizeModel(net1, net2, quantizer)
    encode_quantize_model.eval()
    
    try:
        torch.onnx.export(
            encode_quantize_model,
            (test_input1, test_input2),
            "encode_quantize.onnx",
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=["input1", "input2"],
            output_names=["quantized_feature"],
        )
        
        eq_session = ort.InferenceSession("encode_quantize.onnx")
        onnx_eq_result = eq_session.run(None, {"input1": input1_np, "input2": input2_np})[0]
        
        pytorch_eq_result = quantized_feature.numpy()
        eq_diff = np.max(np.abs(pytorch_eq_result - onnx_eq_result))
        
        print(f"编码+量化最大差异: {eq_diff:.6f}")
        
        if eq_diff < 1e-5:
            print("✓ 编码+量化步骤一致")
        else:
            print("✗ 编码+量化步骤不一致")
            print(f"PyTorch 量化前5个元素: {pytorch_eq_result.flatten()[:5]}")
            print(f"ONNX 量化前5个元素: {onnx_eq_result.flatten()[:5]}")
        
    except Exception as e:
        print(f"编码+量化测试失败: {e}")

if __name__ == "__main__":
    main()
