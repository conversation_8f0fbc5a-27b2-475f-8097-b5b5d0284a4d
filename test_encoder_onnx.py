import torch
import numpy as np
import onnxruntime as ort
from config import BaseConfig
from models.core_model import CoreModel
from utils import load_individual_model_state
import argparse

# 创建简单的参数
class Args:
    def __init__(self):
        self.model_path = 'results/my_experiment_phase3/models/celeba_phase3_epoch5_snr5.pth'
        self.trainset = 'celeba'
        self.testset = 'kodak'
        self.data_path = './data'
        self.model = 'WITT_W/O'
        self.C = 512
        self.quant_code_dim = 128
        self.channel_type = 'rayleigh'
        self.multiple_snr = '5'
        self.distortion_metric = 'MSE'
        self.experiment_name = 'default_exp'
        self.num_workers = 0

def main():
    args = Args()
    cfg = BaseConfig(args)
    
    print("=== 测试编码器 ONNX 转换 ===")
    
    # 1. 创建并加载 PyTorch 模型
    print("1. 创建并加载 PyTorch 模型...")
    net1 = CoreModel(args, cfg)
    net2 = CoreModel(args, cfg)
    
    try:
        checkpoint = torch.load(args.model_path, map_location='cpu')
        load_individual_model_state(net1, checkpoint["net1_state_dict"], 'cpu')
        load_individual_model_state(net2, checkpoint["net2_state_dict"], 'cpu')
        print("✓ PyTorch 模型权重加载成功")
    except Exception as e:
        print(f"✗ PyTorch 模型权重加载失败: {e}")
        return
    
    net1.eval()
    net2.eval()
    
    # 2. 创建测试数据
    print("2. 创建测试数据...")
    test_input1 = torch.rand(2, 3, 64, 64)  # [0, 1] 范围
    test_input2 = torch.rand(2, 3, 64, 64)
    print(f"测试输入形状: {test_input1.shape}, {test_input2.shape}")
    print(f"输入范围: [{test_input1.min():.3f}, {test_input1.max():.3f}]")
    
    # 3. PyTorch 编码器推理
    print("3. PyTorch 编码器推理...")
    with torch.no_grad():
        pytorch_feature1 = net1.encode(test_input1)
        pytorch_feature2 = net2.encode(test_input2)
        pytorch_combined = pytorch_feature1 + pytorch_feature2
        
        print(f"PyTorch 特征1形状: {pytorch_feature1.shape}")
        print(f"PyTorch 特征2形状: {pytorch_feature2.shape}")
        print(f"PyTorch 合并特征形状: {pytorch_combined.shape}")
        print(f"PyTorch 特征1前3个元素: {pytorch_feature1.flatten()[:3]}")
        print(f"PyTorch 特征2前3个元素: {pytorch_feature2.flatten()[:3]}")
        print(f"PyTorch 合并特征前3个元素: {pytorch_combined.flatten()[:3]}")
    
    # 4. 创建编码器模型用于 ONNX 导出
    print("4. 创建编码器模型...")
    
    class EncoderModel(torch.nn.Module):
        def __init__(self, net1, net2):
            super().__init__()
            self.net1 = net1
            self.net2 = net2
        
        def forward(self, input1, input2):
            feature1 = self.net1.encode(input1)
            feature2 = self.net2.encode(input2)
            combined_feature = feature1 + feature2
            return combined_feature
    
    encoder_model = EncoderModel(net1, net2)
    encoder_model.eval()
    
    # 5. 导出 ONNX 模型
    print("5. 导出 ONNX 模型...")
    try:
        torch.onnx.export(
            encoder_model,
            (test_input1, test_input2),
            "encoder_model.onnx",
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=["input1", "input2"],
            output_names=["combined_feature"],
            dynamic_axes={
                "input1": {0: "batch_size"},
                "input2": {0: "batch_size"},
                "combined_feature": {0: "batch_size"},
            },
        )
        print("✓ ONNX 导出成功")
    except Exception as e:
        print(f"✗ ONNX 导出失败: {e}")
        return
    
    # 6. 加载并测试 ONNX 模型
    print("6. 测试 ONNX 模型...")
    try:
        session = ort.InferenceSession("encoder_model.onnx")
        
        # 转换输入为 numpy
        input1_np = test_input1.numpy().astype(np.float32)
        input2_np = test_input2.numpy().astype(np.float32)
        
        # ONNX 推理
        onnx_result = session.run(None, {"input1": input1_np, "input2": input2_np})[0]
        print(f"ONNX 合并特征形状: {onnx_result.shape}")
        print(f"ONNX 合并特征前3个元素: {onnx_result.flatten()[:3]}")
        
        # 比较结果
        pytorch_result_np = pytorch_combined.numpy()
        diff = np.abs(pytorch_result_np - onnx_result)
        max_diff = np.max(diff)
        mean_diff = np.mean(diff)
        
        print(f"最大差异: {max_diff:.6f}")
        print(f"平均差异: {mean_diff:.6f}")
        
        if max_diff < 1e-5:
            print("✓ PyTorch 和 ONNX 编码结果一致")
        else:
            print("✗ PyTorch 和 ONNX 编码结果不一致")
            
            # 详细分析差异
            print("前10个元素对比:")
            for i in range(min(10, pytorch_result_np.size)):
                pt_val = pytorch_result_np.flatten()[i]
                onnx_val = onnx_result.flatten()[i]
                diff_val = abs(pt_val - onnx_val)
                print(f"  [{i}] PyTorch: {pt_val:.6f}, ONNX: {onnx_val:.6f}, 差异: {diff_val:.6f}")
                
            # 统计差异分布
            num_diff = np.sum(diff > 1e-5)
            total_elements = pytorch_result_np.size
            print(f"显著不同元素数量: {num_diff}/{total_elements} ({100*num_diff/total_elements:.1f}%)")
            
            # 检查是否是系统性差异
            print(f"PyTorch 特征范围: [{pytorch_result_np.min():.3f}, {pytorch_result_np.max():.3f}]")
            print(f"ONNX 特征范围: [{onnx_result.min():.3f}, {onnx_result.max():.3f}]")
        
    except Exception as e:
        print(f"✗ ONNX 模型测试失败: {e}")
    
    # 7. 测试单个编码器
    print("\n7. 测试单个编码器...")
    
    class SingleEncoderModel(torch.nn.Module):
        def __init__(self, net):
            super().__init__()
            self.net = net
        
        def forward(self, input_img):
            return self.net.encode(input_img)
    
    single_encoder = SingleEncoderModel(net1)
    single_encoder.eval()
    
    try:
        torch.onnx.export(
            single_encoder,
            test_input1,
            "single_encoder.onnx",
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=["input"],
            output_names=["feature"],
        )
        print("✓ 单个编码器 ONNX 导出成功")
        
        # 测试单个编码器
        single_session = ort.InferenceSession("single_encoder.onnx")
        onnx_single_result = single_session.run(None, {"input": input1_np})[0]
        
        pytorch_single_result = pytorch_feature1.numpy()
        single_diff = np.max(np.abs(pytorch_single_result - onnx_single_result))
        
        print(f"单个编码器最大差异: {single_diff:.6f}")
        
        if single_diff < 1e-5:
            print("✓ 单个编码器 PyTorch 和 ONNX 结果一致")
        else:
            print("✗ 单个编码器 PyTorch 和 ONNX 结果不一致")
        
    except Exception as e:
        print(f"单个编码器测试失败: {e}")

if __name__ == "__main__":
    main()
