import numpy as np
import onnxruntime as ort
import torch

from config import BaseConfig
from models.quantization import DENSEQuantizer
from utils import load_individual_model_state


# 创建简单的参数
class Args:
    def __init__(self):
        self.model_path = (
            "results/my_experiment_phase3/models/celeba_phase3_epoch5_snr5.pth"
        )
        self.trainset = "celeba"
        self.testset = "kodak"
        self.data_path = "./data"
        self.model = "WITT_W/O"
        self.C = 512
        self.quant_code_dim = 128
        self.channel_type = "rayleigh"
        self.multiple_snr = "5"
        self.distortion_metric = "MSE"
        self.experiment_name = "default_exp"
        self.num_workers = 0


def main():
    args = Args()
    cfg = BaseConfig(args)

    print("=== 调试量化器权重问题 ===")

    # 1. 创建并加载 PyTorch 量化器
    print("1. 创建并加载 PyTorch 量化器...")
    quantizer = DENSEQuantizer(
        feature_dim=cfg.encoder_kwargs["embed_dims"][-1], code_dim=cfg.quant_code_dim
    ).to(cfg.device)

    try:
        checkpoint = torch.load(args.model_path)
        load_individual_model_state(
            quantizer, checkpoint["quantizer_state_dict"], cfg.device
        )
        print("✓ PyTorch 量化器权重加载成功")
    except Exception as e:
        print(f"✗ PyTorch 量化器权重加载失败: {e}")
        return

    quantizer.eval()

    # 2. 检查量化器权重
    print("2. 检查量化器权重...")
    print(
        f"feature_to_code_proj 权重形状: {quantizer.feature_to_code_proj.weight.shape}"
    )
    print(f"feature_to_code_proj 偏置形状: {quantizer.feature_to_code_proj.bias.shape}")
    print(
        f"code_to_feature_proj 权重形状: {quantizer.code_to_feature_proj.weight.shape}"
    )
    print(f"code_to_feature_proj 偏置形状: {quantizer.code_to_feature_proj.bias.shape}")

    print(
        f"feature_to_code_proj 权重前3个元素: {quantizer.feature_to_code_proj.weight.flatten()[:3]}"
    )
    print(
        f"feature_to_code_proj 偏置前3个元素: {quantizer.feature_to_code_proj.bias.flatten()[:3]}"
    )

    # 3. 创建测试数据
    print("3. 创建测试数据...")
    test_features = torch.randn(2, 512).to(cfg.device)  # (batch*seq, feature_dim)
    print(f"测试特征形状: {test_features.shape}")
    print(f"测试特征前3个元素: {test_features.flatten()[:3]}")

    # 4. PyTorch 量化器推理
    print("4. PyTorch 量化器推理...")
    with torch.no_grad():
        # 步骤1: 线性投影
        pre_quant_code = quantizer.feature_to_code_proj(test_features)
        print(f"线性投影后形状: {pre_quant_code.shape}")
        print(f"线性投影后前3个元素: {pre_quant_code.flatten()[:3]}")

        # 步骤2: sign 量化
        pytorch_quantized = torch.sign(pre_quant_code)
        print(f"PyTorch 量化后形状: {pytorch_quantized.shape}")
        print(f"PyTorch 量化后前3个元素: {pytorch_quantized.flatten()[:3]}")

        # 使用完整的量化函数
        pytorch_full_quantized = quantizer.quantize_features_to_bits(test_features)
        print(f"PyTorch 完整量化后前3个元素: {pytorch_full_quantized.flatten()[:3]}")

        # 检查是否一致
        if torch.equal(pytorch_quantized, pytorch_full_quantized):
            print("✓ 手动量化与完整量化结果一致")
        else:
            print("✗ 手动量化与完整量化结果不一致")

    # 5. 创建简单的量化器模型用于 ONNX 导出
    print("5. 创建简单的量化器模型...")

    class SimpleQuantizer(torch.nn.Module):
        def __init__(self, quantizer):
            super().__init__()
            self.feature_to_code_proj = quantizer.feature_to_code_proj

        def forward(self, features):
            pre_quant_code = self.feature_to_code_proj(features)
            quantized = torch.sign(pre_quant_code)
            return quantized

    simple_quantizer = SimpleQuantizer(quantizer)
    simple_quantizer.eval()

    # 6. 导出 ONNX 模型
    print("6. 导出 ONNX 模型...")
    try:
        # 将模型移到 CPU 进行 ONNX 导出
        simple_quantizer_cpu = SimpleQuantizer(quantizer.cpu())
        simple_quantizer_cpu.eval()

        torch.onnx.export(
            simple_quantizer_cpu,
            test_features.cpu(),
            "simple_quantizer.onnx",
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=["features"],
            output_names=["quantized"],
        )
        print("✓ ONNX 导出成功")
    except Exception as e:
        print(f"✗ ONNX 导出失败: {e}")
        return

    # 7. 加载并测试 ONNX 模型
    print("7. 测试 ONNX 模型...")
    try:
        session = ort.InferenceSession("simple_quantizer.onnx")

        # 转换输入为 numpy
        test_features_np = test_features.cpu().numpy().astype(np.float32)

        # ONNX 推理
        onnx_result = session.run(None, {"features": test_features_np})[0]
        print(f"ONNX 量化后形状: {onnx_result.shape}")
        print(f"ONNX 量化后前3个元素: {onnx_result.flatten()[:3]}")

        # 比较结果
        pytorch_result_np = pytorch_quantized.cpu().numpy()
        diff = np.abs(pytorch_result_np - onnx_result)
        max_diff = np.max(diff)
        mean_diff = np.mean(diff)

        print(f"最大差异: {max_diff}")
        print(f"平均差异: {mean_diff}")

        if max_diff < 1e-6:
            print("✓ PyTorch 和 ONNX 量化结果一致")
        else:
            print("✗ PyTorch 和 ONNX 量化结果不一致")

            # 详细分析差异
            print("前10个元素对比:")
            for i in range(min(10, pytorch_result_np.size)):
                pt_val = pytorch_result_np.flatten()[i]
                onnx_val = onnx_result.flatten()[i]
                diff_val = abs(pt_val - onnx_val)
                print(
                    f"  [{i}] PyTorch: {pt_val:.6f}, ONNX: {onnx_val:.6f}, 差异: {diff_val:.6f}"
                )

    except Exception as e:
        print(f"✗ ONNX 模型测试失败: {e}")

    # 8. 检查 ONNX 模型的权重
    print("8. 检查 ONNX 模型的权重...")
    try:
        import onnx

        onnx_model = onnx.load("simple_quantizer.onnx")

        print("ONNX 模型的初始化器（权重）:")
        for initializer in onnx_model.graph.initializer:
            print(f"  名称: {initializer.name}, 形状: {initializer.dims}")

            # 获取权重数据
            if initializer.name.endswith(".weight"):
                weight_data = np.frombuffer(
                    initializer.raw_data, dtype=np.float32
                ).reshape(initializer.dims)
                print(f"    前3个元素: {weight_data.flatten()[:3]}")

                # 与 PyTorch 权重比较
                if "feature_to_code_proj" in initializer.name:
                    pytorch_weight = quantizer.feature_to_code_proj.weight.cpu().numpy()
                    weight_diff = np.max(np.abs(pytorch_weight - weight_data))
                    print(f"    与 PyTorch 权重差异: {weight_diff}")

            elif initializer.name.endswith(".bias"):
                bias_data = np.frombuffer(
                    initializer.raw_data, dtype=np.float32
                ).reshape(initializer.dims)
                print(f"    前3个元素: {bias_data.flatten()[:3]}")

                # 与 PyTorch 偏置比较
                if "feature_to_code_proj" in initializer.name:
                    pytorch_bias = quantizer.feature_to_code_proj.bias.cpu().numpy()
                    bias_diff = np.max(np.abs(pytorch_bias - bias_data))
                    print(f"    与 PyTorch 偏置差异: {bias_diff}")

    except Exception as e:
        print(f"检查 ONNX 权重失败: {e}")


if __name__ == "__main__":
    main()
