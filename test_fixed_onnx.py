import torch
from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer
import argparse

# 创建简单的参数
class Args:
    def __init__(self):
        self.model_path = 'results/my_experiment_phase3/models/celeba_phase3_epoch5_snr5.pth'
        self.trainset = 'celeba'
        self.testset = 'kodak'
        self.data_path = './data'
        self.model = 'WITT_W/O'
        self.C = 512
        self.quant_code_dim = 128
        self.channel_type = 'rayleigh'
        self.multiple_snr = '5'
        self.distortion_metric = 'MSE'
        self.experiment_name = 'default_exp'
        self.num_workers = 0

args = Args()
cfg = BaseConfig(args)

# 创建模型实例
net1 = CoreModel(args, cfg).to(cfg.device)
quantizer = DENSEQuantizer(
    feature_dim=cfg.encoder_kwargs['embed_dims'][-1], code_dim=cfg.quant_code_dim
).to(cfg.device)
interleaver = BlockInterleaver().to(cfg.device)

# 修复后的接收端模型
class FixedReceiverModel(torch.nn.Module):
    def __init__(self, net1, net2, quantizer, interleaver):
        super().__init__()
        self.net1 = net1
        self.net2 = net2
        self.quantizer = quantizer
        self.interleaver = interleaver

    def forward(self, interleaved_feature):
        # 动态计算正确的metadata，而不是硬编码
        B, L = interleaved_feature.shape
        # 计算L_feat: L = L_feat * quant_code_dim
        L_feat = L // cfg.quant_code_dim
        
        # 构建正确的metadata
        metadata = {"x_interleaved_shape": torch.Size([L_feat, B, cfg.quant_code_dim])}

        # 解交织
        deinterleaved_feature = self.interleaver.deinterleave(
            interleaved_feature, metadata
        )
        # 使用动态计算的L_feat而不是硬编码的64
        deinterleaved_feature = deinterleaved_feature.reshape(B * L_feat, cfg.quant_code_dim)
        # 反量化
        dequantized_feature = self.quantizer.dequantize_bits_to_features(
            deinterleaved_feature
        ).reshape(B, L_feat, -1)
        # 解码
        recon_image1 = self.net1.decode(dequantized_feature)
        recon_image2 = self.net2.decode(dequantized_feature)
        return recon_image1, recon_image2

# 原始有问题的接收端模型
class BuggyReceiverModel(torch.nn.Module):
    def __init__(self, net1, net2, quantizer, interleaver):
        super().__init__()
        self.net1 = net1
        self.net2 = net2
        self.quantizer = quantizer
        self.interleaver = interleaver

    def forward(self, interleaved_feature):
        B, L = interleaved_feature.shape
        metadata = {"x_interleaved_shape": torch.Size([64, B, L // 64])}  # 硬编码64
        
        deinterleaved_feature = self.interleaver.deinterleave(interleaved_feature, metadata)
        deinterleaved_feature = deinterleaved_feature.reshape(B * 64, cfg.quant_code_dim)  # 硬编码64
        dequantized_feature = self.quantizer.dequantize_bits_to_features(
            deinterleaved_feature
        ).reshape(B, 64, -1)  # 硬编码64
        recon_image1 = self.net1.decode(dequantized_feature)
        recon_image2 = self.net2.decode(dequantized_feature)
        return recon_image1, recon_image2

# 完整的正确流程
def correct_pipeline(input1, input2):
    with torch.no_grad():
        # 编码
        feature1 = net1.encode(input1)
        feature2 = net1.encode(input2)
        feature = feature1 + feature2
        
        B, L_feat, D_feat = feature.shape
        
        # 量化
        quantized_feature = quantizer.quantize_features_to_bits(feature.reshape(B * L_feat, D_feat))
        quantized_feature = quantized_feature.reshape(B, L_feat, cfg.quant_code_dim)
        
        # 交织
        interleaved_feature, metadata = interleaver.interleave(quantized_feature)
        
        # 解交织 (使用正确的metadata)
        deinterleaved_feature = interleaver.deinterleave(interleaved_feature, metadata)
        
        # 反量化
        deinterleaved_feature_flat = deinterleaved_feature.reshape(B * L_feat, cfg.quant_code_dim)
        dequantized_feature = quantizer.dequantize_bits_to_features(deinterleaved_feature_flat)
        dequantized_feature = dequantized_feature.reshape(B, L_feat, -1)
        
        # 解码
        recon_image1 = net1.decode(dequantized_feature)
        recon_image2 = net1.decode(dequantized_feature)
        
        return recon_image1, recon_image2, interleaved_feature

print("=== 测试修复后的ONNX接收端模型 ===")

# 测试不同batch size
for batch_size in [1, 2, 4]:
    print(f"\n--- batch_size = {batch_size} ---")
    
    # 创建测试输入
    dummy_input1 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
    dummy_input2 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
    
    # 正确的流程
    correct_recon1, correct_recon2, interleaved = correct_pipeline(dummy_input1, dummy_input2)
    
    # 修复后的接收端模型
    try:
        fixed_receiver = FixedReceiverModel(net1, net1, quantizer, interleaver)
        fixed_recon1, fixed_recon2 = fixed_receiver(interleaved)
        
        # 计算差异
        diff1 = torch.max(torch.abs(correct_recon1 - fixed_recon1))
        diff2 = torch.max(torch.abs(correct_recon2 - fixed_recon2))
        
        print(f"修复后模型 - 重建图像1最大差异: {diff1.item():.8f}")
        print(f"修复后模型 - 重建图像2最大差异: {diff2.item():.8f}")
        
        if torch.equal(correct_recon1, fixed_recon1) and torch.equal(correct_recon2, fixed_recon2):
            print("✓ 修复后模型结果完全正确")
        else:
            print("✗ 修复后模型仍有差异")
            
    except Exception as e:
        print(f"✗ 修复后模型失败: {e}")
    
    # 原始有问题的接收端模型 (仅在L_feat=64时测试)
    try:
        buggy_receiver = BuggyReceiverModel(net1, net1, quantizer, interleaver)
        buggy_recon1, buggy_recon2 = buggy_receiver(interleaved)
        
        # 计算差异
        diff1 = torch.max(torch.abs(correct_recon1 - buggy_recon1))
        diff2 = torch.max(torch.abs(correct_recon2 - buggy_recon2))
        
        print(f"原始模型 - 重建图像1最大差异: {diff1.item():.8f}")
        print(f"原始模型 - 重建图像2最大差异: {diff2.item():.8f}")
        
    except Exception as e:
        print(f"✗ 原始模型失败: {e}")

print("\n=== 总结 ===")
print("修复的关键点:")
print("1. 动态计算L_feat = L // cfg.quant_code_dim")
print("2. 使用正确的metadata: [L_feat, B, cfg.quant_code_dim]")
print("3. 在所有reshape操作中使用L_feat而不是硬编码的64")
