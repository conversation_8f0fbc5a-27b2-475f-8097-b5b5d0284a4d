import torch
from config import BaseConfig
from models.core_model import CoreModel
from models.interleaver import BlockInterleaver
from models.quantization import DENSEQuantizer
import argparse

# 创建简单的参数
class Args:
    def __init__(self):
        self.model_path = 'results/my_experiment_phase3/models/celeba_phase3_epoch5_snr5.pth'
        self.trainset = 'celeba'
        self.testset = 'kodak'
        self.data_path = './data'
        self.model = 'WITT_W/O'
        self.C = 512
        self.quant_code_dim = 128
        self.channel_type = 'rayleigh'
        self.multiple_snr = '5'
        self.distortion_metric = 'MSE'
        self.experiment_name = 'default_exp'
        self.num_workers = 0

args = Args()
cfg = BaseConfig(args)

# 创建模型实例
net1 = CoreModel(args, cfg).to(cfg.device)
quantizer = DENSEQuantizer(
    feature_dim=cfg.encoder_kwargs['embed_dims'][-1], code_dim=cfg.quant_code_dim
).to(cfg.device)
interleaver = BlockInterleaver().to(cfg.device)

print("=== 逐步调试数值差异 ===")

batch_size = 1
dummy_input1 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)
dummy_input2 = torch.randn(batch_size, 3, 64, 64).to(cfg.device)

with torch.no_grad():
    print("1. 编码阶段")
    feature1 = net1.encode(dummy_input1)
    feature2 = net1.encode(dummy_input2)
    feature = feature1 + feature2
    print(f"   特征形状: {feature.shape}")
    print(f"   特征前3个元素: {feature.flatten()[:3]}")
    
    print("\n2. 量化阶段")
    B, L_feat, D_feat = feature.shape
    feature_flat = feature.reshape(B * L_feat, D_feat)
    quantized_feature_flat = quantizer.quantize_features_to_bits(feature_flat)
    quantized_feature = quantized_feature_flat.reshape(B, L_feat, cfg.quant_code_dim)
    print(f"   量化后形状: {quantized_feature.shape}")
    print(f"   量化后前3个元素: {quantized_feature.flatten()[:3]}")
    
    print("\n3. 交织阶段")
    interleaved_feature, correct_metadata = interleaver.interleave(quantized_feature)
    print(f"   交织后形状: {interleaved_feature.shape}")
    print(f"   正确metadata: {correct_metadata}")
    print(f"   交织后前3个元素: {interleaved_feature.flatten()[:3]}")
    
    print("\n4. 解交织阶段 - 正确方法")
    correct_deinterleaved = interleaver.deinterleave(interleaved_feature, correct_metadata)
    print(f"   正确解交织后形状: {correct_deinterleaved.shape}")
    print(f"   正确解交织后前3个元素: {correct_deinterleaved.flatten()[:3]}")
    
    # 检查交织/解交织是否完全恢复
    if torch.equal(quantized_feature, correct_deinterleaved):
        print("   ✓ 交织/解交织完全恢复")
    else:
        diff = torch.max(torch.abs(quantized_feature - correct_deinterleaved))
        print(f"   ✗ 交织/解交织有差异: {diff}")
    
    print("\n5. 解交织阶段 - ONNX方法")
    B_inter, L_inter = interleaved_feature.shape
    L_feat_calc = L_inter // cfg.quant_code_dim
    onnx_metadata = {"x_interleaved_shape": torch.Size([L_feat_calc, B_inter, cfg.quant_code_dim])}
    print(f"   计算的L_feat: {L_feat_calc}, 实际L_feat: {L_feat}")
    print(f"   ONNX metadata: {onnx_metadata}")
    
    onnx_deinterleaved = interleaver.deinterleave(interleaved_feature, onnx_metadata)
    print(f"   ONNX解交织后形状: {onnx_deinterleaved.shape}")
    print(f"   ONNX解交织后前3个元素: {onnx_deinterleaved.flatten()[:3]}")
    
    # 检查两种解交织方法的差异
    if torch.equal(correct_deinterleaved, onnx_deinterleaved):
        print("   ✓ 两种解交织方法结果相同")
    else:
        diff = torch.max(torch.abs(correct_deinterleaved - onnx_deinterleaved))
        print(f"   ✗ 两种解交织方法有差异: {diff}")
        
        # 详细比较metadata
        print(f"   正确metadata: {correct_metadata}")
        print(f"   ONNX metadata: {onnx_metadata}")
    
    print("\n6. 反量化阶段")
    # 正确方法
    correct_flat = correct_deinterleaved.reshape(B * L_feat, cfg.quant_code_dim)
    correct_dequant_flat = quantizer.dequantize_bits_to_features(correct_flat)
    correct_dequant = correct_dequant_flat.reshape(B, L_feat, -1)
    print(f"   正确反量化后形状: {correct_dequant.shape}")
    print(f"   正确反量化后前3个元素: {correct_dequant.flatten()[:3]}")
    
    # ONNX方法
    onnx_flat = onnx_deinterleaved.reshape(B * L_feat_calc, cfg.quant_code_dim)
    onnx_dequant_flat = quantizer.dequantize_bits_to_features(onnx_flat)
    onnx_dequant = onnx_dequant_flat.reshape(B, L_feat_calc, -1)
    print(f"   ONNX反量化后形状: {onnx_dequant.shape}")
    print(f"   ONNX反量化后前3个元素: {onnx_dequant.flatten()[:3]}")
    
    # 检查反量化差异
    if torch.equal(correct_dequant, onnx_dequant):
        print("   ✓ 两种反量化方法结果相同")
    else:
        diff = torch.max(torch.abs(correct_dequant - onnx_dequant))
        print(f"   ✗ 两种反量化方法有差异: {diff}")
    
    print("\n7. 解码阶段")
    # 正确方法
    correct_recon = net1.decode(correct_dequant)
    print(f"   正确解码后形状: {correct_recon.shape}")
    print(f"   正确解码后前3个元素: {correct_recon.flatten()[:3]}")
    
    # ONNX方法
    onnx_recon = net1.decode(onnx_dequant)
    print(f"   ONNX解码后形状: {onnx_recon.shape}")
    print(f"   ONNX解码后前3个元素: {onnx_recon.flatten()[:3]}")
    
    # 最终差异
    final_diff = torch.max(torch.abs(correct_recon - onnx_recon))
    print(f"   最终重建图像差异: {final_diff}")

print("\n=== 结论 ===")
print("通过逐步比较，我们可以确定差异出现在哪个阶段")
